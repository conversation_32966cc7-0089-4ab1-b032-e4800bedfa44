@if (!nexxID) {
  <app-preload-image
    [ngClass]="{ 'absolute left-0 top-0 h-full': !autoSize }"
    class="w-full"
    [fill]="!autoSize"
    [src]="image"
    [imageWidth]="800"
    [groupHoverEffect]="!withoutHoverEffect"
    [lowResSrc]="imageLowRes"
    [focalPoint]="focalPoint"
    [imageRatio]="imageRatio"
  >
  </app-preload-image>
}

@if (nexxID && !inlineVideo) {
  <lib-preview-video
    [autoSize]="autoSize"
    [ngClass]="{ 'absolute left-0 top-0 h-full': !autoSize }"
    class="w-full h-full absolute left-0 top-0"
    [poster]="image"
  >
  </lib-preview-video>
}

@if (nexxID && inlineVideo) {
  <div
    [ngClass]="{ 'absolute left-0 top-0 h-full': !autoSize }"
    class="w-full h-full absolute left-0 top-0"
    [nexxID]="nexxID"
  ></div>
}

<div
  class="flex flex-col justify-end items-start absolute left-2.5 mr-2.5 bottom-2.5 z-10"
>
  <ng-template #tag let-color="color" let-text="text" let-show="show">
    @if (show) {
      <h4
        class="transition bg-{{
          color
        }} mt-2.5 bg-opacity-75 font-medium mr-auto p-4 px-4 py-2 uppercase rounded text-xs text-white "
        [ngClass]="{ 'group-hover:bg-opacity-100': withoutHoverEffect }"
      >
        {{ text }}
      </h4>
    }
  </ng-template>

  <ng-container
    *ngTemplateOutlet="tag; context: { color: 'red', text: 'NEU', show: new }"
  ></ng-container>

  @if ((!!meta?.girlInfos && meta?.girlInfos > 1) || !!meta?.images || !!meta?.videos) {
    <h4
      class="transition bg-gray-900 mt-2.5 bg-opacity-75 font-medium mr-auto p-4 px-4 py-2 uppercase rounded text-xs text-white"
      [ngClass]="{ 'group-hover:bg-opacity-100': withoutHoverEffect }"
    >
      @if (meta?.girlInfos && meta?.girlInfos > 1) {
        <span class="whitespace-nowrap">{{ meta.girlInfos }} Galerie{{ meta.girlInfos === 1 ? "" : "n" }}</span>
      }
      @if (meta?.girlInfos && meta?.girlInfos > 1 && (meta?.images || meta?.videos)) {
        ·
      }
      @if (meta?.images) {
        <span class="whitespace-nowrap">{{ meta.images }} Bild{{ meta.images === 1 ? "" : "er" }}</span>
      }
      @if (meta?.images && meta?.videos) {
        ·
      }
      @if (meta?.videos) {
        <span class="whitespace-nowrap">{{ meta.videos }} Video{{ meta.videos === 1 ? "" : "s" }}</span>
      }
    </h4>
  }
</div>
