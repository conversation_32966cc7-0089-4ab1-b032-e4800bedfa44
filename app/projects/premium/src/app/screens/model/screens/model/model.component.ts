import {
  Component,
  computed,
  effect,
  ElementRef,
  inject,
  input,
  QueryList,
  untracked,
  ViewChildren,
} from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import {
  BreadcrumbsComponent,
  distinctJSON,
  ModelCardComponent,
  MonthPipe,
} from '@pb/ui';
import { Apollo } from 'apollo-angular';
import moment from 'moment';
import { AccountService } from 'projects/premium/src/app/services/account.service';
import { AnalyticsService } from 'projects/premium/src/app/services/analytics.service';
import { FavoritesService } from 'projects/premium/src/app/services/favorites/favorites.service';
import {
  FocalPointPipe,
  GetFocalPoint,
} from 'projects/premium/src/app/shared/pipes/focal-point.pipe';
import { IsNew } from 'projects/premium/src/app/utils/newCheck';
import { EMPTY } from 'rxjs';
import {
  ArticlePreviewComponent,
  CategorySlugPipe,
  FavoriteStarComponent,
  GalleryCardComponent,
  LockBadgeComponent,
} from '../../../../shared';

import { SlicePipe } from '@angular/common';
import { map } from 'rxjs/operators';
import { IPreview } from '../../../../models/preview';
import {
  FieldMediaSlideshow,
  IGirlDetailData,
  IGirlInfo,
  PublicImage,
} from './definitions/models';
import { GIRL_DETAIL_DATA_QUERY } from './definitions/queries';
import { rxResource } from '@angular/core/rxjs-interop';
import { SubscriptionPopupService } from '../../../../components/subscription-popup/subscription-popup.service';
import { parseNumberOrNull } from '../../../../utils/parseNumberOrNull';

export interface IGirlDetailMeta {
  tags: { entityLabel: string }[];
  category: string;
  description: string;
  attributes: [string, string][];
  galleries: number;
  videos: number;
  images: number;
}

type MediaFilter = 'pictures' | 'videos' | undefined;

type GalleryListItem = IGirlInfo & { displayItems: FieldMediaSlideshow[] };
declare var upScore: any;

@Component({
  templateUrl: './model.component.html',
  styleUrls: ['./model.component.css'],
  imports: [
    FavoriteStarComponent,
    MonthPipe,
    RouterLink,
    SlicePipe,
    ArticlePreviewComponent,
    FocalPointPipe,
    GalleryCardComponent,
    CategorySlugPipe,
    BreadcrumbsComponent,
    ModelCardComponent,
    LockBadgeComponent,
  ],
})
export class ModelComponent {
  readonly subscriptionType = this.accountService.subscriptionType;

  public showAllGalleryImages = false;
  public readonly gallery_id = input<string>(); // Input binding of route param gallery_id, only for shooting page
  public readonly girl_id = input<string>(); // Input binding of route param girl_id
  firstImageUrl: string;
  customSourceLand: string;
  customSectionCategory: string;
  @ViewChildren('gallery') galleryElements: QueryList<ElementRef<HTMLElement>>;
  protected readonly girl = rxResource({
    request: () => this.girl_id(),
    loader: ({ request }) => {
      if (!request) {
        return EMPTY; // Avoid making the request if `id()` is undefined
      }
      return this.apollo
        .query<{ girlById: IGirlDetailData }>({
          query: GIRL_DETAIL_DATA_QUERY,
          variables: { girlId: request },
        })
        .pipe(
          map((v) => v.data.girlById),
          distinctJSON(),
        );
    },
  });
  protected readonly girlInfos = computed(() => {
    return [...(this.girl.value()?.reverseGirlGirlInfo.entities || [])]
      .filter((v) => !!v)
      .map((v, i) => ({ ...v, seen: i !== 0 }));
  });

  protected readonly girlInfoGalleries = computed(() => {
    const girlInfos = this.girlInfos();
    return girlInfos.map(
      (info): GalleryListItem => ({
        ...info,
        fieldImageCount: parseNumberOrNull(info?.fieldImageCount),
        fieldVideoCount: parseNumberOrNull(info?.fieldVideoCount),
        displayItems: (info.queryGalleries?.entities || [])
          .filter((v) => !!v)
          .reverse()
          .reduce((all, now): FieldMediaSlideshow[] => {
            const items = [
              ...all,
              ...now.fieldMediaSlideshow.map((v) => ({
                ...v,
                entity: {
                  ...v.entity,
                  isNew: IsNew(now.fieldPublishDate?.value),
                },
              })),
            ];

            const isGirlOfTheDay = info.reverseFieldGirlInfosNode?.entities &&
                                 info.reverseFieldGirlInfosNode.entities.length > 0;

            // Hide videos when plus user and girl of the day
            if (!(this.subscriptionType() === 'plus' && isGirlOfTheDay)) {
              items.push(
                ...now.fieldVideos.map((v) => ({
                  ...v,
                  entity: {
                    ...v.entity,
                    isNew: IsNew(now.fieldPublishDate?.value),
                  },
                })),
              );
            }

            return items;
          }, [])
          .filter((v) => !!v.entity?.mid),
      }),
    );
  });
  protected readonly headerGallery = computed(() => {
    const girlInfos = this.girlInfos();
    const girlId = this.girl_id();
    const girl = this.girl.value();
    const galleryId = this.gallery_id();
    return girlInfos.map((girlInfo): IPreview => {
      const image = (girlInfo.queryGalleries?.entities || []).filter(
        (v) => !!v,
      )[0];
      const imageUrl = girlInfo.fieldPublicImages?.[0]?.url;
      // @TODO CLEAN UP!
      if (girlInfo && girlInfo?.id?.toString() === galleryId) {
        this.route.queryParams.subscribe((params) => {
          if (!!params.return) {
            // open gallery and save return x button
            this.router.navigate(
              [
                '/girl-info',
                this.gallery_id(),
                image?.fieldMediaSlideshow[0]?.entity?.mid,
              ],
              {
                queryParams: { return: params.return },
                queryParamsHandling: 'merge',
              },
            );
          }
          if (!this.firstImageUrl && imageUrl) {
            this.firstImageUrl = imageUrl;
            this.meta.addTag({ name: 'og:image', content: this.firstImageUrl });
          }
        });
      }

      return {
        id: girlInfo.id,
        image: imageUrl,
        text: girl.name,
        title: girlInfo.fieldCategory?.entity?.name,
        link: ['/girl', girlId, girlInfo.id],
        focalPoint: GetFocalPoint(image?.fieldImage?.entity),
      };
    });
  });
  protected readonly currentGirlGallery = computed(() => {
    return this.girlInfoGalleries().find(
      (galleryItem) => galleryItem.id?.toString() === this.gallery_id(),
    );
  });
  protected readonly metaInformation = computed(() => {
    // const girl = this.girl.value();
    const girlInfos = this.girlInfos();
    const galleryId = this.gallery_id();
    const currGirlGallery = this.currentGirlGallery();
    const infos = galleryId ? currGirlGallery : girlInfos;
    const latestInfo = girlInfos.find((v) => v.fieldCategory); // find first truthy fieldCategory in girlInfos
    return {
      galleries: Array.isArray(infos) ? infos.length : 0,
      category: latestInfo?.fieldCategory?.entity?.name,
      description: latestInfo?.description,
      tags: latestInfo?.queryFieldTags?.entities || [],
      release: latestInfo?.release
        ? new Date(latestInfo?.release).toLocaleDateString()
        : '',
      attributes: !latestInfo
        ? []
        : ([
            !latestInfo.bustsize || !latestInfo.waistsize || !latestInfo.hipsize
              ? undefined
              : [
                  'Maße',
                  [
                    latestInfo.bustsize,
                    latestInfo.waistsize,
                    latestInfo.hipsize,
                  ].join('-'),
                ],
            !latestInfo.height
              ? undefined
              : ['Größe', `${latestInfo.height} cm`],
            !latestInfo.birthday?.value
              ? undefined
              : [
                  'Alter',
                  `${moment().diff(moment(latestInfo.birthday!.value), 'years')} Jahre`,
                ],
            !latestInfo.birthday?.value
              ? undefined
              : [
                  'Geburtsdatum',
                  [
                    !!latestInfo.birthday?.value
                      ? new Date(latestInfo.birthday.value).toLocaleDateString(
                          'de-DE',
                          {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          },
                        )
                      : '',
                  ],
                ],
            !latestInfo.eyecolor?.entity.entityLabel
              ? undefined
              : ['Augenfarbe', latestInfo.eyecolor?.entity.entityLabel],
            !latestInfo.haircolor?.entity.entityLabel
              ? undefined
              : ['Haarfarbe', latestInfo.haircolor?.entity.entityLabel],
            !latestInfo.fieldCity?.entity.entityLabel
              ? undefined
              : [
                  'Geburtsort',
                  [latestInfo.fieldCity?.entity?.entityLabel]
                    .filter((v) => !!v)
                    .join(', '),
                ],
            !latestInfo.hometown?.entity.entityLabel
              ? undefined
              : [
                  'Wohnort',
                  [latestInfo.hometown?.entity?.entityLabel]
                    .filter((v) => !!v)
                    .join(', '),
                ],
            !latestInfo.fieldCountry?.entity.entityLabel
              ? undefined
              : [
                  'Land',
                  [latestInfo.fieldCountry?.entity.entityLabel]
                    .filter((v) => !!v)
                    .join(', '),
                ],
          ].filter((v) => !!v) as [string, string][]),
      videos: Array.isArray(infos)
        ? infos.reduce(
            (sum, item) => sum + (parseNumberOrNull(item.fieldVideoCount) || 0),
            0,
          )
        : parseNumberOrNull(infos?.fieldVideoCount),
      images: Array.isArray(infos)
        ? infos.reduce(
            (sum, item) => sum + (parseNumberOrNull(item.fieldImageCount) || 0),
            0,
          )
        : parseNumberOrNull(infos?.fieldImageCount),
    };
  });
  private readonly subscriptionPopupService: SubscriptionPopupService = inject(
    SubscriptionPopupService,
  );

  constructor(
    public favoritesService: FavoritesService,
    private apollo: Apollo,
    private route: ActivatedRoute,
    private router: Router,
    private titleService: Title,
    private meta: Meta,
    analyticsService: AnalyticsService,
    private accountService: AccountService,
  ) {
    effect(() => {
      analyticsService.sendEvent('girl', this.girl_id());
    });

    effect(() => {
      this.titleService.setTitle(
        `${this.girl.value()?.name || ''} | Playboy ${this.accountService.subscriptionTypeTitle()}`,
      );
    });

    effect(() => {
      const girl = this.girl.value();
      if (!this.gallery_id()) {
        untracked(() => {
          if (!girl) {
            return;
          }
          const fullName = girl.name;
          const [firstName, lastName] = fullName.split(' ');
          if (girl) {
            if (typeof upScore !== 'undefined') {
              let upScoreConfig = {};
              upScoreConfig = {
                config: {
                  domain: 'playboy.de',
                  article: 'Girl/Model',
                  track_positions: false,
                },
                data: {
                  section: 'Model',
                  taxonomy: '', // Tags fehlen
                  object_id: 'aa_' + this.girl_id(),
                  pubdate: new Date(
                    girl.reverseGirlGirlInfo.entities[0].release * 1000,
                  ).toLocaleDateString(),
                  author: '',
                  object_type: 'person',
                  custom_source: this.customSourceLand,
                  custom_app: 0,
                  custom_video: 0,
                  custom_audio: 0,
                  content_type: 1,
                  content_blocked: 1,
                  conversion: 2,
                  user_status: 2,
                },
              };
              console.log('upScore Config:', upScoreConfig);

              upScore(upScoreConfig);
            }

            this.meta.removeTag(`name='og:site_name'`);
            this.meta.removeTag(`name='og:type'`);
            this.meta.removeTag(`name='og:title'`);
            this.meta.removeTag(`name='og:description'`);
            this.meta.removeTag(`name='og:locale'`);
            this.meta.removeTag(`name='og:url'`);
            this.meta.removeTag(`name='og:image'`);
            this.meta.removeTag(`name='article:published_time'`);
            this.meta.removeTag(`name='profile:first_name'`);
            this.meta.removeTag(`name='profile:last_name'`);
            this.meta.removeTag(`name='profile:gender'`);
            this.meta.removeTag(`name='description'`);
            this.meta.removeTag(`name='author'`);
            this.meta.removeTag(`name='meta'`);

            this.meta.addTag({
              name: 'og:site_name',
              content: 'Playboy All Access',
            });
            this.meta.addTag({ name: 'og:type', content: 'profile' });
            this.meta.addTag({ name: 'og:title', content: girl.name });
            this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
            this.meta.addTag({
              name: 'og:url',
              content: `https://premium.playboy.de/girl/${this.girl_id()}`,
            });
            // this.meta.addTag({ name: 'og:image:width', content: '' });
            // this.meta.addTag({ name: 'og:image:height', content: '' });
            // this.meta.addTag({ name: 'og:image:type', content: '' });
            this.meta.addTag({
              name: 'profile:first_name',
              content: firstName || girl.name,
            });
            this.meta.addTag({
              name: 'profile:last_name',
              content: lastName || '',
            });
            this.meta.addTag({ name: 'profile:gender', content: 'female' });
            this.meta.addTag({ name: 'title', content: girl.name });
            this.meta.addTag({ name: 'title', content: girl.name });
          }
        });
      } else {
        const metaI = this.metaInformation();
        const girlInfos = this.girlInfos();
        untracked(() => {
          if (!girl) {
            return;
          }
          // META DATA
          const landAttribute = metaI?.attributes?.find(
            (attr) => attr[0] === 'Land',
          );
          const land = landAttribute ? landAttribute[1] : 'Unbekannt';

          const categoryAttribute = metaI?.category;
          this.customSectionCategory = categoryAttribute
            ? categoryAttribute
            : '';

          if (metaI && metaI.description) {
            this.meta.updateTag({
              name: 'description',
              content: metaI.description,
            });
            this.meta.updateTag({
              name: 'og:description',
              content: metaI.description,
            });
          }
          if (metaI && metaI.tags) {
            this.meta.updateTag({
              name: 'keywords',
              content: metaI.tags.map((tag: any) => tag.entityLabel).join(', '),
            });
          }
          this.customSourceLand = land;
          // GIRL INFOS
          const targetGirlInfo = girlInfos.find(
            (info) => info.id === Number(this.gallery_id()),
          );
          const fullName = girl.name;
          const [firstName, lastName] = fullName.split(' ');
          if (girl) {
            if (typeof upScore !== 'undefined') {
              let upScoreConfig = {};
              upScoreConfig = {
                config: {
                  domain: 'playboy.de',
                  article: 'Girl-info/Shooting',
                  track_positions: false,
                },
                data: {
                  section: this.customSectionCategory,
                  taxonomy: '',
                  object_id: 'aa_' + this.girl_id(),
                  pubdate: new Date(
                    girl.reverseGirlGirlInfo.entities[0].release * 1000,
                  ).toLocaleDateString(),
                  author: this.getCredit(targetGirlInfo),
                  object_type: 'gallery',
                  custom_source: this.customSourceLand,
                  custom_app: 0,
                  custom_video: 0,
                  custom_audio: 0,
                  content_type: 1,
                  content_blocked: 1,
                  conversion: 2,
                  user_status: 2,
                },
              };

              console.log('upScore Config:', upScoreConfig);

              upScore(upScoreConfig);
            }
            this.meta.removeTag(`name='og:site_name'`);
            this.meta.removeTag(`name='og:type'`);
            this.meta.removeTag(`name='og:title'`);
            this.meta.removeTag(`name='og:description'`);
            this.meta.removeTag(`name='og:locale'`);
            this.meta.removeTag(`name='og:url'`);
            this.meta.removeTag(`name='og:image'`);
            this.meta.removeTag(`name='article:published_time'`);
            this.meta.removeTag(`name='profile:first_name'`);
            this.meta.removeTag(`name='profile:last_name'`);
            this.meta.removeTag(`name='profile:gender'`);
            this.meta.removeTag(`name='description'`);
            this.meta.removeTag(`name='author'`);
            this.meta.removeTag(`name='meta'`);

            this.meta.addTag({
              name: 'og:site_name',
              content: 'Playboy All Access',
            });
            this.meta.addTag({ name: 'og:type', content: 'article' });
            this.meta.addTag({ name: 'og:title', content: girl.name });
            this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
            this.meta.addTag({
              name: 'og:url',
              content: `https://premium.playboy.de/girl/${this.girl_id()}/${this.gallery_id()}`,
            });
            this.meta.addTag({ name: 'profile:gender', content: 'female' });
            this.meta.addTag({
              name: 'author',
              content: this.getCredit(targetGirlInfo),
            });
            this.meta.addTag({ name: 'title', content: girl.name });
            this.meta.addTag({
              name: 'profile:first_name',
              content: firstName || girl.name,
            });
            this.meta.addTag({
              name: 'profile:last_name',
              content: lastName || '',
            });
            const date = new Date(
              girl.reverseGirlGirlInfo.entities[0].release * 1000,
            );

            const pad = (num) => num.toString().padStart(2, '0');

            const year = date.getFullYear();
            const month = pad(date.getMonth() + 1);
            const day = pad(date.getDate());
            const hours = pad(date.getHours());
            const minutes = pad(date.getMinutes());
            const seconds = pad(date.getSeconds());

            const offset = -date.getTimezoneOffset();
            const sign = offset >= 0 ? '+' : '-';
            const offsetHours = pad(Math.floor(Math.abs(offset) / 60));
            const offsetMinutes = pad(Math.abs(offset) % 60);

            const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${sign}${offsetHours}${offsetMinutes}`;

            this.meta.addTag({
              name: 'article:published_time',
              content: formattedDate,
            });

            // this.meta.addTag({
            //   name: 'keywords',
            //   content: Array.isArray(this.field_tags) ? this.field_tags.join(', ') : String(this.field_tags)
            // });
          }
        });
      }
    });
  }

  public isShootingGallery(): boolean {
    return !!this.gallery_id();
  }

  getCredit(girlInfo: IGirlInfo): string {
    if (!girlInfo?.queryGalleries?.entities) {
      return ''; // Return empty string if entities are undefined or null
    }

    // Extract the fieldCredit values, remove duplicates, and join them into a string
    const uniqueCredits = Array.from(
      new Set(
        girlInfo.queryGalleries.entities
          .filter((entity) => entity) // Remove any falsy (null, undefined) entities
          .map((entity) => entity.fieldCredit), // Extract the fieldCredit values
      ),
    );

    return uniqueCredits.join(', '); // Convert array to comma-separated string
  }

  getMetaInformationText(): string {
    const meta = this.metaInformation();
    if (!meta) return '';

    const parts: string[] = [];

    if (meta.galleries) {
      parts.push(`${meta.galleries}&nbsp;Galerie${meta.galleries === 1 ? '' : 'n'}`);
    }

    if (meta.images) {
      parts.push(`${meta.images}&nbsp;Bild${meta.images === 1 ? '' : 'er'}`);
    }

    if (meta.videos) {
      parts.push(`${meta.videos}&nbsp;Video${meta.videos === 1 ? '' : 's'}`);
    }

    let result = parts.join(' · ');

    if (this.getCredit(this.currentGirlGallery())) {
      const creditText = `Credit: ${this.getCredit(this.currentGirlGallery())}`;
      result += result ? ` · ${creditText}` : creditText;
    }

    return result;
  }

  showPaywall(
    fieldPlusAccess: boolean,
    publicImages: Array<PublicImage> = [],
  ): void {
    this.subscriptionPopupService.open(fieldPlusAccess, {
      images: publicImages.map((i) => i.url),
    });
  }
}
