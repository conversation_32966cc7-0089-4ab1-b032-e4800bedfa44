export interface IFieldMediaSlideshowEntity {
  mid: number;
  fieldMediaImage: {
    url?: string;
  };
}

export interface IGirlDetailData {
  name: string;
  reverseGirlGirlInfo: ReverseGirlGirlInfo;
  mainImages: FieldGirlMainImage[];
}

export interface ReverseGirlGirlInfo {
  entities: IGirlInfo[];
}

export interface PublicImage {
  url: string;
}
export interface PublicImageDerivative {
  derivative: {
    url: string;
  };
}

export interface IGirlInfo {
  id: number;
  name: FluffyName;
  height: number | null;
  changed: number | null;
  hipsize: number | null;
  bustsize: number | null;
  waistsize: number | null;
  descriptorWeek: number | null;
  descriptorYear: string | null;
  description: string | null;
  descriptorMonth: number | null;
  queryMainImages?: QueryMainImages;
  queryGalleries?: QueryGalleries;
  queryFieldTags?: QueryTags;
  release?: number;
  fieldCategory: Category;
  fieldPlusAccess?: boolean | string;
  birthday?: { value: string };
  fieldCity?: { entity: { entityLabel: string } };
  hometown?: { entity: { entityLabel: string } };
  fieldCountry?: { entity: { entityLabel: string } };
  eyecolor?: { entity: { entityLabel: string } };
  haircolor?: { entity: { entityLabel: string } };
  descriptorCategory?: { entity: { name: string } };
  fieldPublicImages?: PublicImage[];
  fieldAdditionalGirls?: {
    entity: {
      name: string;
      mainImages?: FieldGirlMainImage[];
      reverseGirlGirlInfo?: ReverseGirlGirlInfo;
    };
    targetId: number;
  }[];
  fieldImageCount?: number | string | null;
  fieldVideoCount?: number | string | null;
  reverseFieldGirlInfosNode?: {
    entities?: ({ entityId: number } | null)[];
  };
}

export interface FieldGirlMainImage {
  entity: {
    fieldFocalPointX: number;
    fieldFocalPointY: number;
    fieldMediaImage: {
      url: string;
    };
  };
}

export interface Category {
  entity: CategoryEntity;
}

export interface CategoryEntity {
  name: PurpleName;
}

export enum PurpleName {
  Coverstar = 'Coverstar',
  PlaymateDESMonats = 'Playmate des Monats',
  WorldExclusives = 'World Exclusives',
}

export enum FluffyName {
  PamelaAnderson = 'Pamela Anderson',
}

export interface QueryGalleries {
  entities: QueryGalleriesEntity[];
}
export interface QueryTags {
  entities: QueryTagsEntity[];
}

export interface QueryTagsEntity {
  entityId: string;
  entityLabel: string;
}
export interface QueryGalleriesEntity {
  name: string;
  fieldCredit: string;
  fieldPublishDate: { value: string };
  fieldMediaSlideshow: FieldMediaSlideshow[];
  fieldVideos: INexxVideoItem[];
  fieldImage: {
    entity: {
      fieldFocalPointX: number;
      fieldFocalPointY: number;
      fieldMediaImage: {
        url: string;
        width: number;
        height: number;
      };
    };
  };
}

export interface INexxVideoItem {
  entity: {
    mid: string;
    fieldVideo: {
      title: string;
      itemId: number;
    };
  };
}

export interface FieldMediaSlideshow {
  entity: IFieldMediaSlideshowEntity;
}

export interface QueryMainImages {
  entities: IFieldMediaSlideshowEntity[];
}
