import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  Component,
  Inject,
  OnChanges,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  SimpleChanges,
} from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Subscription } from 'rxjs';
import { AccountService } from '../../../services/account.service';
import { PublicService } from '../../../services/public.service';
import { GalleryCardComponent } from '../../../shared';
import { MonthDatePipe } from '../../../shared/pipes/month-date.pipe';
import { parseNumberOrNull } from '../../../utils/parseNumberOrNull';
import { BreadcrumbsComponent } from '@pb/ui';

declare var upScore: any;

@Component({
  selector: 'app-public-girl',
  templateUrl: './public-girl.component.html',
  styleUrls: ['./public-girl.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    GalleryCardComponent,
    RouterLink,
    MonthDatePipe,
    BreadcrumbsComponent,
  ],
})
export class PublicGirlComponent implements OnInit, OnDestroy, OnChanges {
  loading = true;
  isBrowser: boolean;
  girlId: string;
  name: string;
  description: string;
  images: any[];
  weight: string;
  hipsize: string;
  birthday: string;
  bustsize: string;
  field_city: string;
  eyecolor_1: string;
  haircolor_1: string;
  height: string;
  field_province: string;
  waistsize: string;
  field_release_date_format: any;
  release: string;
  field_tags: string[];
  field_credit: string;
  field_category: string;
  firstname: string;
  lastname: string;
  focalPoint = { x: 0.5, y: 0.5 };
  field_main_focal_point_x: number;
  field_main_focal_point_y: number;
  field_image_count: number;
  field_video_count: number;
  girlInfoId: string;
  id: string;
  descriptor_country_ref: string;
  protected field_category_slug: string;
  private sub: Subscription;

  constructor(
    private publicData: PublicService,
    private route: ActivatedRoute,
    private meta: Meta,
    private accountService: AccountService,
    private router: Router,
    private titleService: Title,
    @Inject(PLATFORM_ID) platformId: Object,
  ) {
    // Get id from URL
    this.girlId = this.route.snapshot.paramMap.get('girl_id');
    this.isBrowser = isPlatformBrowser(platformId);

    // If the user is logged in and subscribed, redirect to the full girl page
    this.accountService.Subscribed.subscribe((subscribed) => {
      if (subscribed) {
        return this.router.navigate(['/girl/' + this.girlId]);
      }
    });

    // Initialize the release date format
    this.field_release_date_format = new Date(this.release);
  }

  ngOnInit(): void {
    // Call API on initialization
    this.loadGirlInfo();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['girlId'] && !changes['girlId'].firstChange) {
      // If girlId changes, reload the girl info
      this.loadGirlInfo();
    }
  }

  // Method to load girl information from the API
  loadGirlInfo(): void {
    let parser = null;
    if (this.isBrowser) {
      parser = new DOMParser();
    }

    this.sub = this.publicData.getGirl(this.girlId).subscribe({
      next: (data) => {
        if (!!data && !!data.images) {
          // Set data to variables
          this.name = this.decodeHtmlEntities(data.name);
          this.description = !!parser
            ? parser.parseFromString(data.description, 'text/html').body
                .textContent || ''
            : '';

          this.images = data.images;
          this.weight = data.weight;
          this.waistsize = data.waistsize;
          this.field_province = data.field_province;
          this.hipsize = data.hipsize;
          this.haircolor_1 = data.haircolor_1;
          this.eyecolor_1 = data.eyecolor_1;
          this.field_city = data.field_city;
          this.bustsize = data.bustsize;
          this.birthday = data.birthday;
          this.release = data.release;
          this.field_tags = Array.isArray(data.field_tags)
            ? data.field_tags.map((tag) => this.decodeHtmlEntities(tag))
            : data.field_tags
              ? [this.decodeHtmlEntities(data.field_tags)]
              : [];
          this.field_credit = data.field_credit;
          this.field_category = data.field_category;
          this.field_category_slug = this.field_category
            ?.toLowerCase()
            ?.replace(' ', '-'); // replace first space in field category for slug
          this.descriptor_country_ref = data.descriptor_country_ref;
          this.firstname = data.firstname;
          this.lastname = data.lastname;
          this.girlInfoId = data.id;
          this.focalPoint = {
            x: data.field_main_focal_point_x,
            y: data.field_main_focal_point_y,
          };
          this.field_main_focal_point_x = data.field_main_focal_point_x;
          this.field_main_focal_point_y = data.field_main_focal_point_y;
          this.field_image_count = parseNumberOrNull(data.field_image_count);
          this.field_video_count = parseNumberOrNull(data.field_video_count);
          this.id = data.id;

          this.field_release_date_format = new Date(
            this.release,
          ).toLocaleDateString('de-DE', {
            year: 'numeric',
            month: 'long',
          });

          this.meta.removeTag(`name='og:site_name'`);
          this.meta.removeTag(`name='og:type'`);
          this.meta.removeTag(`name='og:title'`);
          this.meta.removeTag(`name='og:description'`);
          this.meta.removeTag(`name='og:locale'`);
          this.meta.removeTag(`name='og:url'`);
          this.meta.removeTag(`name='og:image'`);
          this.meta.removeTag(`name='article:published_time'`);
          this.meta.removeTag(`name='profile:first_name'`);
          this.meta.removeTag(`name='profile:last_name'`);
          this.meta.removeTag(`name='profile:gender'`);
          this.meta.removeTag(`name='description'`);
          this.meta.removeTag(`name='author'`);
          this.meta.removeTag(`name='meta'`);

          this.titleService.setTitle(this.name + ' | Playboy Premium');
          this.meta.addTag({
            name: 'og:site_name',
            content: 'Playboy All Access',
          });
          this.meta.addTag({ name: 'og:type', content: 'profile' });
          this.meta.addTag({ name: 'og:title', content: this.name });
          this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
          this.meta.addTag({
            name: 'og:url',
            content: `https://premium.playboy.de/girl/${this.girlId}`,
          });
          this.meta.addTag({ name: 'og:image', content: this.images[0] });
          this.meta.addTag({ name: 'og:image:width', content: '' });
          this.meta.addTag({ name: 'og:image:height', content: '' });
          this.meta.addTag({ name: 'og:image:type', content: '' });
          this.meta.addTag({
            name: 'profile:first_name',
            content: this.firstname,
          });
          this.meta.addTag({
            name: 'profile:last_name',
            content: this.lastname,
          });
          this.meta.addTag({ name: 'profile:gender', content: 'female' });
          this.meta.addTag({ name: 'title', content: this.name });
          this.meta.addTag({ name: 'description', content: '' });

          if (typeof upScore !== 'undefined') {
            let upScoreConfig = {};
            upScoreConfig = {
              config: {
                domain: 'playboy.de',
                article: 'Girl/Model',
                track_positions: false,
              },
              data: {
                section: 'Model',
                taxonomy: this.field_tags + ', ' + this.name,
                object_id: 'aa_' + this.girlId,
                pubdate: this.release,
                author: '',
                object_type: 'person',
                custom_source: this.descriptor_country_ref,
                custom_app: 0,
                custom_video: 0,
                custom_audio: 0,
                content_type: 1,
                content_blocked: 1,
                conversion: 0,
                user_status: 0,
              },
            };

            console.log('upScore Config:', upScoreConfig);

            upScore(upScoreConfig);
          }

          this.loading = false;
        }
      },
      error: (error) => {
        console.error(error, 'error');
      },
    });
  }

  ngOnDestroy(): void {
    // Unsubscribe to prevent memory leaks
    this.sub.unsubscribe();
  }

  getMetaText(): string {
    const parts: string[] = [];

    if (this.images && this.images.length > 0) {
      parts.push(`${this.images.length}&nbsp;Galerie${this.images.length === 1 ? '' : 'n'}`);
    }

    if (this.field_image_count) {
      parts.push(`${this.field_image_count}&nbsp;Bild${this.field_image_count === 1 ? '' : 'er'}`);
    }

    if (this.field_video_count) {
      parts.push(`${this.field_video_count}&nbsp;Video${this.field_video_count === 1 ? '' : 's'}`);
    }

    return parts.join(' · ');
  }

  private decodeHtmlEntities(text: string | null | undefined): string {
    if (!text) {
      return '';
    }

    if (typeof document !== 'undefined') {
      const textarea = document.createElement('textarea');
      textarea.innerHTML = text;
      return textarea.value;
    }

    return text;
  }
}
