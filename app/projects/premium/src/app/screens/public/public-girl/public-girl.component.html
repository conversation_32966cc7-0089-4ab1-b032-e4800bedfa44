<lib-breadcrumbs
  [breadcrumbs]="[
    {
      link: '/p/categories/' + field_category_slug,
      label: field_category,
    },
    { link: '/p/girl/' + girlId, label: name },
  ]"
  class="mt-8 ml-5 md:ml-28-5 mr-5 md:mr-28-5 md:mt-0"
>
</lib-breadcrumbs>

<div class="gallery-hero">
  <div class="sm:h-auto h-screen-3/4 flex flex-col aspect-auto sm:aspect-2/1">
    @if (!loading) {
      <app-gallery-card
        class="w-full h-full max-h-full"
        [image]="images[0].image"
        [focalPoint]="images[0].focalPoint"
        [withoutCard]="true"
      ></app-gallery-card>
    }
  </div>
</div>

<div class="md:mx-31 flex-col mx-auto">
  <h2 class="my-6 mt-20">{{ name }}</h2>
  <h4 class="text-golden mb-6">
    @if (images && images.length > 0) {
      <span class="whitespace-nowrap">{{ images.length }} Galerie{{ images.length == 1 ? "" : "n" }}</span>
    }
    @if (
      images &&
      images.length > 0 &&
      field_image_count &&
      (field_video_count || field_credit)
    ) {
      ·
    }
    @if (field_image_count) {
      <span class="whitespace-nowrap">{{ field_image_count }} Bild{{ field_image_count == 1 ? "" : "er" }}</span>
    }
    @if (field_image_count && field_video_count) {
      ·
    }
    @if (field_video_count) {
      <span class="whitespace-nowrap">{{ field_video_count }} Video{{ field_video_count == 1 ? "" : "s" }}</span>
    }
  </h4>
  <!--    <h4 class="flex flex-1 items-center justify-start cursor-pointer" (click)="openPopup()">-->
  <!--        <img src='/assets/gallery/Star.svg'>-->
  <!--        {{ name }} zu meinen <br> Favoriten hinzufügen-->
  <!--    </h4>-->
  <!-- <div class="mt-8 font-garamond text-lg">
        <div class="
            font-stag
            md:border-golden
            justify-center
            md:border-l
            md:my-2
            md:pl-18
            py-6
        ">
            @if(hipsize){
            <div class="flex flex-row">
                <p class="w-32">Hüftgröße:</p>
                <p class="flex flex-1">{{ hipsize }} cm</p>
            </div>
            }
            @if(birthday){
            <div class="flex flex-row">
                <p class="w-32">Geburtsdatum:</p>
                <p class="flex flex-1">{{ birthday }}</p>
            </div>
            }
            @if(bustsize){
            <div class="flex flex-row">
                <p class="w-32">Brustumfang:</p>
                <p class="flex flex-1">{{ bustsize }} cm</p>
            </div>
            }
            @if(field_city){
            <div class="flex flex-row">
                <p class="w-32">Stadt:</p>
                <p class="flex flex-1">{{ field_city }}</p>
            </div>
            }
            @if(eyecolor_1){
            <div class="flex flex-row">
                <p class="w-32">Augenfarbe:</p>
                <p class="flex flex-1">{{ eyecolor_1 }}</p>
            </div>
            }
            @if(haircolor_1){
            <div class="flex flex-row">
                <p class="w-32">Haarfarbe:</p>
                <p class="flex flex-1">{{ haircolor_1 }}</p>
            </div>
            }
            @if(height){
            <div class="flex flex-row">
                <p class="w-32">Größe:</p>
                <p class="flex flex-1">{{ height }} cm</p>
            </div>
            }
            @if(field_province){
            <div class="flex flex-row">
                <p class="w-32">Provinz:</p>
                <p class="flex flex-1">{{ field_province }}</p>
            </div>
            }
            @if(waistsize){
            <div class="flex flex-row">
                <p class="w-32">Taillenumfang:</p>
                <p class="flex flex-1">{{ waistsize }} cm</p>
            </div>
            }

        </div>
    </div> -->
  <p [innerHTML]="description" class="mt-8 font-georgia text-lg"></p>

  <hr class="w-full my-15 border-golden" />
  <p class="text-4xl font-bauer mb-10 md:mb-20">Galerien</p>
</div>
<div
  [class.single]="images.length === 1"
  [class.triple]="images.length >= 3"
  class="gallery-list mt-6 grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-6 mb-52 mx-5"
>
  @if (!loading) {
    @for (image of images; track image) {
      <a [routerLink]="['/p/girl/info', image.girlInfoId]">
        <app-gallery-card
          class="w-full shooting-preview cursor-pointer"
          [name]="field_category"
          [title]="image.release | pbMonthDateFormat"
          [imageRatio]="1"
          [focalPoint]="image.focalPoint"
          [credit]="field_credit"
          [image]="image.image"
          [meta]="{
            images: image?.fieldImageCount,
            videos: image?.fieldVideoCount,
            girlInfos: 0,
          }"
        >
        </app-gallery-card>
      </a>
    }
  }
</div>
