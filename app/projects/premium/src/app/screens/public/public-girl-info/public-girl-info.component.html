<lib-breadcrumbs
  [breadcrumbs]="[
    {
      link: '/p/categories/' + field_category_slug,
      label: field_category,
    },
    { link: '/p/girl/' + girl_id, label: name },
  ]"
  class="mt-8 ml-5 md:ml-28-5 mr-5 md:mr-28-5 md:mt-0"
>
</lib-breadcrumbs>

<div class="gallery-hero">
  <div class="sm:h-auto h-screen-3/4 flex flex-col aspect-auto sm:aspect-2/1">
    @if (!loading) {
      <app-gallery-card
        class="w-full h-full max-h-full"
        [imageRatio]="1"
        [image]="images[0]"
        [focalPoint]="focalPoint"
        [withoutCard]="true"
      ></app-gallery-card>
    }
  </div>
</div>

<div class="md:w-5/6 flex flex-col mx-auto md:mt-20 mt-10">
  <div class="flex items-center justify-start mb-4 md:mb-6">
    <h2 class="inline-flex">{{ name }}</h2>
  </div>
  <h4 class="sm:mb-3">
    @if (field_category) {
      {{ field_category }}
    }
    @if (descriptor_month) {
      · {{ descriptor_month | month }}
    }
    @if (descriptor_year) {
      {{ descriptor_year }}
    }
  </h4>

  <h4 class="text-golden sm:mb-3">
    @if (field_image_count) {
      <span class="whitespace-nowrap">{{ field_image_count }} Bild{{ field_image_count == 1 ? "" : "er" }}</span>
    }
    @if (field_image_count && (field_video_count || field_credit)) {
      ·
    }
    @if (field_video_count) {
      <span class="whitespace-nowrap">{{ field_video_count }} Video{{ field_video_count == 1 ? "" : "s" }}</span>
    }
    @if (field_video_count && field_credit) {
      ·
    }
    @if (field_credit) {
      Credit: {{ field_credit }}
    }
  </h4>
</div>
<div
  (click)="openPopup(fieldPlusAccess, images)"
  class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 md:gap-6 mt-10 md:mt-20"
>
  @for (image of images; track image; let index = $index) {
    <app-preload-image
      class="gallery-image rounded-lg"
      [imageRatio]="1"
      [fill]="true"
      [src]="image"
      [focalPoint]="
        index === 0
          ? focalPoint
          : index === 1
            ? focalPointSecond
            : index === 2
              ? focalPointThird
              : focalPoint
      "
    >
    </app-preload-image>
  } @empty {
    <p>There are no images.</p>
  }
</div>
<div
  (click)="openPopup(fieldPlusAccess, images)"
  class="flex my-6 md:my-8 lg:my-12 px-3 md:px-0 mx-6 justify-center md:justify-end"
>
  <a
    class="new-btn uppercase font-inter text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
  >
    Alle anzeigen
  </a>
</div>

<div class="md:w-5/6 flex flex-col mx-auto">
  <p [innerHTML]="description" class="mt-3 sm:mt-8 font-georgia text-lg"></p>

  <h3 class="my-10 md:my-15 md:mt-20 w-full text-left" id="title-proposal">
    In dieser Galerie
  </h3>

  <div class="model-preview">
    <a [routerLink]="['/p/girl/', girl_id]" class="inline-block w-max">
      <lib-model-card
        [image]="images?.[0] || null"
        [loading]="loading"
        [name]="name"
      ></lib-model-card>
    </a>
  </div>
</div>
