import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  Component,
  inject,
  Inject,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { <PERSON><PERSON><PERSON>tizer, Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import {
  BreadcrumbsComponent,
  ModelCardComponent,
  MonthPipe,
  PreloadImageComponent,
} from '@pb/ui';
import { Apollo } from 'apollo-angular';
import { Subscription } from 'rxjs';
import { AccountService } from '../../../services/account.service';
import { PublicService } from '../../../services/public.service';
import { GalleryCardComponent } from '../../../shared';
import { SubscriptionPopupService } from '../../../components/subscription-popup/subscription-popup.service';
import { parseNumberOrNull } from '../../../utils/parseNumberOrNull';

declare var upScore: any;

@Component({
  selector: 'app-public-girl-info',
  templateUrl: './public-girl-info.component.html',
  styleUrls: ['./public-girl-info.component.css'],
  standalone: true,
  imports: [
    PreloadImageComponent,
    BreadcrumbsComponent,
    CommonModule,
    RouterLink,
    GalleryCardComponent,
    ModelCardComponent,
    MonthPipe,
  ],
})
export class PublicGirlInfoComponent implements OnInit, OnDestroy {
  loading = true;
  isBrowser: boolean;
  girlInfoId: string;
  name: string;
  description: string;
  images: string[];
  release: string;
  field_credit: string;
  field_tags: string[];
  girl_id: string;
  focalPoint = { x: 0.5, y: 0.5 };
  focalPointSecond = { x: 0.5, y: 0.5 };
  focalPointThird = { x: 0.5, y: 0.5 };
  field_main_focal_point_x: string;
  field_main_focal_point_y: string;
  field_second_focal_point_x: string;
  field_second_focal_point_y: string;
  field_third_focal_point_x: string;
  field_third_focal_point_y: string;
  field_image_count: number;
  field_video_count: number;
  descriptor_country_ref: string;
  field_category: string;
  field_category_slug: string;
  fieldPlusAccess: boolean;
  meta = {
    galleries: 0,
    images: 0,
    videos: 0,
  };
  id: string;
  private sub: Subscription;
  private readonly subscriptionPopupService: SubscriptionPopupService = inject(
    SubscriptionPopupService,
  );
  protected descriptor_month: number;
  protected descriptor_year: number;

  constructor(
    private publicData: PublicService,
    private route: ActivatedRoute,
    private metaService: Meta,
    private accountService: AccountService,
    private router: Router,
    private apollo: Apollo,
    private titleService: Title,
    private sanitizer: DomSanitizer,
    @Inject(PLATFORM_ID) platformId: Object,
  ) {
    this.girlInfoId = this.route.snapshot.paramMap.get('girl_info_id');
    this.isBrowser = isPlatformBrowser(platformId);
  }

  ngOnInit(): void {
    this.loadGirlInfo();
  }

  loadGirlInfo(): void {
    let parser = null;
    if (this.isBrowser) {
      parser = new DOMParser();
    }

    this.sub = this.publicData.getGirlInfo(this.girlInfoId).subscribe({
      next: (data) => {
        if (!!data && !!data.images) {
          this.name = this.decodeHtmlEntities(data.name);
          this.description = !!parser
            ? parser.parseFromString(data.description, 'text/html').body
                .textContent || ''
            : '';
          this.images = data.images;
          this.release = data.release;
          this.field_category = data.field_category;
          this.descriptor_month = data.descriptor_month;
          this.descriptor_year = data.descriptor_year;
          this.field_credit = this.decodeHtmlEntities(data.field_credit);
          this.girl_id = data.girl_id;
          this.descriptor_country_ref = data.descriptor_country_ref;
          this.field_tags = Array.isArray(data.field_tags)
            ? data.field_tags.map((tag) => this.decodeHtmlEntities(tag))
            : data.field_tags
              ? [this.decodeHtmlEntities(data.field_tags)]
              : [];
          this.field_main_focal_point_x = data.field_main_focal_point_x;
          this.field_main_focal_point_y = data.field_main_focal_point_y;
          this.field_second_focal_point_x = data.field_second_focal_point_x;
          this.field_second_focal_point_y = data.field_second_focal_point_y;
          this.field_third_focal_point_x = data.field_third_focal_point_x;
          this.field_third_focal_point_y = data.field_third_focal_point_y;
          this.focalPoint = {
            x: parseInt(this.field_main_focal_point_x),
            y: parseInt(this.field_main_focal_point_y),
          };
          this.focalPointSecond = {
            x: parseInt(this.field_second_focal_point_x),
            y: parseInt(this.field_second_focal_point_y),
          };
          this.focalPointThird = {
            x: parseInt(this.field_third_focal_point_x),
            y: parseInt(this.field_third_focal_point_y),
          };
          this.field_image_count = parseNumberOrNull(data.field_image_count);
          this.field_video_count = parseNumberOrNull(data.field_video_count);
          this.field_category = data.field_category;
          this.field_category_slug = this.field_category
            ?.toLowerCase()
            ?.replace(' ', '-'); // replace first space in field category for slug

          this.fieldPlusAccess = data.field_plus_access;

          this.metaService.removeTag(`name='og:site_name'`);
          this.metaService.removeTag(`name='og:type'`);
          this.metaService.removeTag(`name='og:title'`);
          this.metaService.removeTag(`name='og:description'`);
          this.metaService.removeTag(`name='og:locale'`);
          this.metaService.removeTag(`name='og:url'`);
          this.metaService.removeTag(`name='og:image'`);
          this.metaService.removeTag(`name='article:published_time'`);
          this.metaService.removeTag(`name='profile:first_name'`);
          this.metaService.removeTag(`name='profile:last_name'`);
          this.metaService.removeTag(`name='profile:gender'`);
          this.metaService.removeTag(`name='description'`);
          this.metaService.removeTag(`name='author'`);
          this.metaService.removeTag(`name='meta'`);

          this.titleService.setTitle(this.name + ' | Playboy Premium');
          this.metaService.addTag({
            name: 'og:site_name',
            content: 'Playboy All Access',
          });
          this.metaService.addTag({ name: 'og:type', content: 'article' });
          this.metaService.addTag({ name: 'og:title', content: this.name });
          this.metaService.addTag({ name: 'og:locale', content: 'de_DE' });
          this.metaService.addTag({
            name: 'og:url',
            content: `https://premium.playboy.de/girl/${this.girlInfoId}/${this.girl_id}`,
          });
          this.metaService.addTag({
            name: 'og:image',
            content: this.images[0],
          });
          this.metaService.addTag({
            name: 'author',
            content: this.field_credit,
          });
          this.metaService.addTag({
            name: 'profile:gender',
            content: 'female',
          });
          this.metaService.addTag({ name: 'title', content: this.name });
          this.metaService.addTag({
            name: 'description',
            content: this.description,
          });
          this.metaService.addTag({
            name: 'og:description',
            content: this.description,
          });

          this.metaService.addTag({
            name: 'keywords',
            content: Array.isArray(this.field_tags)
              ? this.field_tags.join(', ')
              : String(this.field_tags),
          });

          this.metaService.addTag({
            name: 'article:published_time',
            content: this.release,
          });

          if (typeof upScore !== 'undefined') {
            let upScoreConfig = {};
            upScoreConfig = {
              config: {
                domain: 'playboy.de',
                article: 'Girl-info/Shooting',
                track_positions: false,
              },
              data: {
                section: this.field_category,
                taxonomy: this.field_tags + ', ' + this.name,
                object_id: 'aa_' + this.girlInfoId,
                pubdate: this.release,
                author: this.field_credit,
                object_type: 'gallery',
                custom_source: this.descriptor_country_ref,
                custom_app: 0,
                custom_video: 0,
                custom_audio: 0,
                content_type: 1,
                content_blocked: 1,
                conversion: 0,
                user_status: 0,
              },
            };

            // Ausgabe in der Konsole
            console.log('upScore Config:', upScoreConfig);

            upScore(upScoreConfig);
          }

          this.loading = false;

          this.accountService.Subscribed.subscribe((subscribed) => {
            if (subscribed) {
              this.router.navigate([
                '/girl/' + this.girl_id + '/' + this.girlInfoId,
              ]);
            }
          });
        }
      },
      error: (error) => {
        console.error(error, 'error');
      },
    });
  }

  public openPopup(
    fieldPlusAccess: boolean,
    publicImages: Array<string> = [],
  ): void {
    this.subscriptionPopupService.open(fieldPlusAccess, {
      images: publicImages,
    });
  }

  ngOnDestroy(): void {
    this.sub.unsubscribe();
  }

  getMetaText(): string {
    const parts: string[] = [];

    if (this.field_image_count) {
      parts.push(`${this.field_image_count}&nbsp;Bild${this.field_image_count === 1 ? '' : 'er'}`);
    }

    if (this.field_video_count) {
      parts.push(`${this.field_video_count}&nbsp;Video${this.field_video_count === 1 ? '' : 's'}`);
    }

    if (this.field_credit) {
      parts.push(`Credit: ${this.field_credit}`);
    }

    return parts.join(' · ');
  }

  private decodeHtmlEntities(text: string | null | undefined): string {
    if (!text) {
      return '';
    }

    if (typeof document !== 'undefined') {
      const textarea = document.createElement('textarea');
      textarea.innerHTML = text;
      return textarea.value;
    }

    return text;
  }
}
