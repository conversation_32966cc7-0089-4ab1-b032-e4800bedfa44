<div
  [class.light]="data.light"
  class="relative popup-container overflow-auto py-14 px-4 md:p-15 md:pb-6 h-full w-full rounded-none sm:rounded-lg"
  id="popup"
>
  <button
    (click)="dialogRef.close()"
    aria-label="schließen"
    class="absolute top-5 right-5 close-btn"
  >
    <svg
      height="32"
      viewBox="0 0 32 32"
      width="32"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        [attr.fill]="data.light ? 'black' : 'white'"
        d="M8.53329 25.3307L6.66663 23.4641L14.1333 15.9974L6.66663 8.53073L8.53329 6.66406L16 14.1307L23.4666 6.66406L25.3333 8.53073L17.8666 15.9974L25.3333 23.4641L23.4666 25.3307L16 17.8641L8.53329 25.3307Z"
      />
    </svg>
  </button>

  <div
    class="popup-content md:grid grid-cols-2 grid-rows-2 md:gap-8 md:gap-y-0"
  >
    <div>
      <div
        class="logo-container flex flex-row items-center justify-center md:justify-start gap-3 py-5"
      >
        <svg
          fill="none"
          height="32"
          viewBox="0 0 155 32"
          width="154"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            [attr.fill]="data.light ? 'black' : 'white'"
            d="M107.823 16.8909C107.823 16.8909 109.179 16.9165 109.557 16.8141C110.382 16.5911 110.796 15.9585 110.793 15.2309C110.793 14.708 110.653 14.2217 109.891 13.9036C109.539 13.7537 109.077 13.7646 108.67 13.7537C108.222 13.7427 107.808 13.7427 107.808 13.7427L107.823 16.8909ZM107.808 24.4671C107.808 24.4671 108.831 24.5366 109.542 24.3501C110.591 24.0795 110.892 23.3775 110.903 22.5072C110.914 21.7723 110.624 21.1361 109.825 20.8801C109.15 20.6644 107.812 20.7375 107.812 20.7375L107.808 24.4671ZM100.566 9.56703C100.566 9.56703 105.01 9.54509 108.545 9.55972C110.195 9.59628 111.273 9.66575 112.351 9.89977C114.661 10.4007 116.439 12.0607 116.406 14.1705C116.37 16.5545 114.797 18.5729 112.065 18.7521C115.083 18.8252 116.956 21.1105 116.901 23.5676C116.839 26.2259 114.712 28.2881 111.926 28.3868C109.198 28.4819 100.588 28.4307 100.588 28.4307L100.577 24.4598H102.18V13.8012H100.562L100.566 9.56703Z"
          />
          <path
            [attr.fill]="data.light ? 'black' : 'white'"
            d="M127.165 24.6834C128.98 24.6944 129.504 22.4055 129.504 18.9647C129.504 15.5276 129.005 12.9316 127.033 12.9316C125.28 12.9316 124.561 15.5276 124.561 18.9647C124.561 22.4055 125.339 24.6725 127.165 24.6834ZM117.965 18.8587C117.965 13.4544 121.536 9.05936 127.025 9.07033C132.771 9.0813 136.089 13.4508 136.089 18.8587C136.089 24.2629 132.518 28.6141 127.025 28.647C121.687 28.6763 117.965 24.2629 117.965 18.8587Z"
          />
          <path
            [attr.fill]="data.light ? 'black' : 'white'"
            d="M30.7263 13.823L30.7153 24.5218L29.0432 24.5108V28.4451H38.848V24.5437H36.8827L36.8643 21.8379C36.8643 21.8379 39.5813 21.911 40.3844 21.805C44.1941 21.3077 45.4554 18.346 45.4774 15.6987C45.5031 12.7918 44.0841 10.4554 40.3 9.78622C39.2037 9.59243 37.627 9.56317 35.8083 9.55586C32.5706 9.54855 29.0176 9.5778 29.0176 9.5778V13.823H30.7263ZM36.8827 13.812C36.8827 13.812 38.1697 13.7133 38.7637 13.9034C39.42 14.1155 39.5923 14.7261 39.5923 15.8779C39.5923 17.0297 38.8883 17.7025 38.3713 17.7829C37.7517 17.878 36.8827 17.8158 36.8827 17.8158V13.812Z"
          />
          <path
            [attr.fill]="data.light ? 'black' : 'white'"
            d="M47.0076 13.7889L47.0039 9.55469H56.6987V13.7962H54.748L54.7406 24.4621H57.5823V21.2225L62.085 21.2188L62.0777 28.444H47.0039V24.4621L48.7126 24.4694L48.7089 13.7816L47.0076 13.7889Z"
          />
          <path
            [attr.fill]="data.light ? 'black' : 'white'"
            d="M70.6942 18.8494L73.2939 18.8421L72.2855 13.4049L70.6942 18.8494ZM69.8728 9.55469H75.9302L80.2349 24.4365H81.5293V28.444L72.8355 28.422V24.4146L74.5002 24.4365L74.2105 22.7691H69.8141L69.2201 24.4365H71.1378L71.1415 28.422H64.0134L64.0098 24.4292L65.3738 24.4401L69.8728 9.55469Z"
          />
          <path
            [attr.fill]="data.light ? 'black' : 'white'"
            d="M81.0781 13.7962V9.55469H90.5052L90.4942 13.7962H89.0495L91.0736 17.1528L92.9692 13.8072H91.6969V9.55469H98.5133V13.7962H97.3473L92.9142 21.0799V24.4584H95.1839V28.4476H85.0308V24.4511L86.7212 24.4548L86.7248 20.8202L82.1561 13.7962H81.0781Z"
          />
          <path
            [attr.fill]="data.light ? 'black' : 'white'"
            d="M136.695 13.7806V9.53906H146.137V13.7806L144.795 13.7623L146.672 16.969L148.557 13.7696L147.325 13.7842V9.53906H154.13V13.7806L152.862 13.7623L148.634 20.6584V24.4501H150.581V28.443H140.67V24.4465H142.36L142.353 20.8924L137.799 13.7806H136.695Z"
          />
          <path
            [attr.fill]="data.light ? 'black' : 'white'"
            d="M20.7865 0C20.7865 0 20.7865 0.358333 20.7865 0.979932C20.7865 1.60519 20.7425 2.09515 20.7425 2.40595C20.7425 5.30187 20.5629 7.62007 20.1632 9.31667C19.6279 11.814 18.5095 13.8178 16.8998 15.423C16.7642 15.5582 16.7642 15.6899 16.9438 15.869C18.2418 17.4742 18.9568 19.0794 18.9568 20.6846C18.9568 22.7359 18.6892 24.3849 18.2418 25.5879C18.1062 25.8548 17.9302 26.1218 17.6625 26.3009C16.7238 26.9262 15.0261 27.6392 12.7015 28.4838C12.5218 28.5277 12.4338 28.44 12.4338 28.3047C12.4338 28.2608 12.4778 27.8586 12.5695 27.1456C12.5695 26.9664 12.5255 26.8787 12.4815 26.8348C12.3934 26.7909 12.2138 26.8348 11.9901 26.9701C9.97709 28.0414 8.95042 28.5753 8.95042 28.5753C8.63875 28.1292 8.50308 27.6392 8.50308 27.1492C8.50308 26.524 8.68275 26.034 8.99442 25.6793C7.38474 25.321 6.09039 24.7433 5.10405 24.0303C4.0297 23.182 3.49437 22.202 3.49437 21.1344C3.49437 19.5292 4.41471 17.9459 6.31039 16.4541C7.61207 15.4303 9.75343 14.7794 12.1221 14.582C10.8718 14.2236 9.30609 13.602 7.42874 12.7099C6.08672 12.0846 4.74838 10.973 3.40637 9.36786C1.93235 7.67491 0.993676 6.06973 0.590339 4.46454C0.31167 3.52117 0.135668 2.40595 0 1.11522C1.69768 2.05128 2.99569 2.76429 3.89037 3.29813C4.96471 3.92338 6.12706 4.85944 7.3774 5.97466C8.71942 7.17764 9.70209 8.24898 10.4611 9.22891C12.2028 11.591 13.5448 13.3315 14.4835 14.5344C14.4835 14.4906 14.4835 13.5106 14.4835 11.6824C14.4835 7.04235 16.5845 3.1665 20.7865 0ZM11.5794 31.6942C11.5904 32.0489 11.4914 32.0964 11.3118 31.8295C11.1321 31.6065 10.7764 31.2518 10.1934 30.8057C9.61409 30.3596 9.21075 30.1366 9.03108 30.1366C8.89542 30.1366 8.49575 30.3157 7.91274 30.6704C7.28573 30.9812 6.97407 31.2042 6.88606 31.2042C6.84206 31.1165 6.7504 30.6265 6.6624 29.8221C6.52673 28.9738 6.43873 28.44 6.43873 28.2608C6.43873 28.0378 6.52673 28.0378 6.7064 28.1255C6.7944 28.2133 7.19773 28.4363 7.91274 28.7508C8.62775 29.0616 9.03108 29.2407 9.16309 29.2407C9.20709 29.2407 9.61042 29.0616 10.2814 28.663C10.9524 28.3083 11.3558 28.0853 11.3998 28.0853C11.3998 28.1292 11.4951 29.8002 11.4584 29.2407C11.4548 29.2042 11.4878 29.6027 11.5318 30.4474C11.5721 31.1604 11.5648 31.3176 11.5794 31.6942ZM17.7908 28.173C17.7908 28.5314 17.7028 28.7508 17.5672 28.8861C16.5845 29.1969 15.6898 29.4199 14.8868 29.6868C13.4128 30.089 12.6501 30.3559 12.5181 30.3559C12.4301 30.3559 12.3824 30.2682 12.3824 30.1329C12.3824 29.8221 12.4264 29.5991 12.5621 29.4638C13.4128 29.153 14.2158 28.9299 14.9308 28.7069C16.3168 28.2169 17.2115 27.95 17.6112 27.95C17.7468 27.95 17.7908 28.0378 17.7908 28.173ZM8.13274 17.2073C7.4654 17.2073 6.9264 17.7485 6.9264 18.4103C6.9264 19.0758 7.46907 19.6133 8.13274 19.6133C8.80008 19.6133 9.33909 19.0721 9.33909 18.4103C9.34275 17.7448 8.80008 17.2073 8.13274 17.2073Z"
          />
        </svg>

        <div
          class="text-base leading-4 px-4 py-2 uppercase text-center rounded-4xl bg-golden logo-tile"
        >
          {{ data.title }}
        </div>
      </div>

      <!--   image preview mobile -->
      @if (data.images.length > 0) {
        <div
          class="grid grid-flow-row auto-cols-fr grid-cols-3 gap-2 my-10 image-grid image-grid--mobile md:hidden md:mt-5"
        >
          @for (image of data.images; let index = $index; track index) {
            @if (index < 3) {
              <app-preload-image [imageRatio]="1" [fill]="true" [src]="image">
              </app-preload-image>
            }
          }
        </div>
      }

      <h2 class="text-2xl md:text-4xl my-10 mx-6 md:mx-0 md:mt-5">
        {{ data.text }}
      </h2>
      <ul class="flex flex-col gap-2 my-10 mx-6 md:mx-0">
        @for (item of data.list; track item) {
          <li
            class="inline-flex flex-row gap-3 items-center text-sm md:text-base m-0"
          >
            <svg
              width="16"
              viewBox="0 0 12 9"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.727273 4.75L1.59091 3.86364L4.59091 6.81818L10.7955 0.636363L11.6818 1.52273L4.59091 8.59091L0.727273 4.75Z"
                [attr.fill]="data.light ? 'black' : 'white'"
              />
            </svg>

            <span>{{ item }}</span>
          </li>
        }
      </ul>

      <!-- Umschalter für 4-WÖCHENTLICH und JÄHRLICH -->
      <div
        class="my-10 mx-6 md:mx-0 grid grid-rows-2 gap-2 lg:grid-rows-1 lg:grid-cols-2"
      >
        <button
          (click)="switchToYearly()"
          [class.active]="!isMonthly()"
          class="toggle-button rounded-lg border-2 py-3 px-4"
        >
          <div class="grid grid-rows-2 gap-1">
            <div class="flex flex-row justify-between gap-1">
              <span class="text-sm md:text-base font-bold text-left">
                Jährlich
              </span>
              <span
                class="flex items-center justify-center py-1 px-2 rounded-lg bg-green-400 text-xs uppercase text-black"
              >
                20 % sparen
              </span>
            </div>
            <span class="text-xs md:text-sm text-left"
              >{{ data.priceYearly }} pro Woche</span
            >
          </div>
        </button>
        <button
          (click)="switchToMonthly()"
          [class.active]="isMonthly()"
          class="toggle-button rounded-lg border-2 py-3 px-4"
        >
          <div class="grid grid-rows-2 gap-1">
            <div class="flex flex-row justify-between gap-1">
              <span class="text-sm md:text-base font-bold text-left">
                4-wöchentlich
              </span>
            </div>
            <span class="text-xs md:text-sm text-left"
              >{{ data.priceMonthly }} pro Woche</span
            >
          </div>
        </button>
      </div>

      <div
        class="action-btns grid grid-flow-row items-center mt-10 mx-6 md:mx-0 gap-2 text-center md:text-left md:gap-3"
      >
        @if (isMonthly()) {
          <a
            class="unlock-link bg-golden text-sm uppercase p-3 rounded text-center tracking-wider"
            [class.button-monthly]="isMonthly()"
            [class.button-yearly]="!isMonthly()"
            href="https://login.playboy.de/register?product={{
              data.productMonthly
            }}&return={{ frontendReturnUrl }}"
            >JETZT FREISCHALTEN</a
          >
        } @else {
          <a
            class="unlock-link bg-golden text-sm uppercase p-3 rounded text-center tracking-wider"
            [class.button-monthly]="isMonthly()"
            [class.button-yearly]="!isMonthly()"
            href="https://login.playboy.de/register?product={{
              data.productYearly
            }}&return={{ frontendReturnUrl }}"
            >JETZT FREISCHALTEN</a
          >
        }
        @if (!(accountService.LoggedIn | async)) {
          @if (isMonthly()) {
            <a
              href="https://login.playboy.de?product={{
                data.productMonthly
              }}&return={{ frontendReturnUrl }}"
              class="existing-account md:hidden"
              id="loginLinkM"
            >
              Ich habe bereits einen Account
            </a>
          }
          @if (!isMonthly()) {
            <a
              href="https://login.playboy.de?product={{
                data.productYearly
              }}&return={{ frontendReturnUrl }}"
              class="existing-account md:hidden"
              id="loginLinkY"
            >
              Ich habe bereits einen Account
            </a>
          }
        }
      </div>
    </div>

    @if (!(accountService.LoggedIn | async)) {
      @if (isMonthly()) {
        <a
          href="https://login.playboy.de?product={{
            data.productMonthly
          }}&return={{ frontendReturnUrl }}"
          class="existing-account hidden md:inline"
          id="loginLinkM"
        >
          Ich habe bereits einen Account
        </a>
      }
      @if (!isMonthly()) {
        <a
          href="https://login.playboy.de?product={{
            data.productYearly
          }}&return={{ frontendReturnUrl }}"
          class="existing-account hidden md:inline"
          id="loginLinkY"
        >
          Ich habe bereits einen Account
        </a>
      }
    }

    <!--   image preview desktop -->
    @if (data.images.length > 0) {
      <div
        class="grid-cols-2 grid-rows-2 gap-4 hidden md:grid content-start image-grid image-grid--desktop"
      >
        @for (image of data.images; let index = $index; track index) {
          @if (index < 3) {
            <app-preload-image [fill]="true" [src]="image"></app-preload-image>
          }
        }
      </div>
    }
  </div>
</div>
