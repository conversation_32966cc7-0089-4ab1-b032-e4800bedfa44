# This file describes an application. You can have multiple applications
# in the same project.
#
# See https://docs.platform.sh/user_guide/reference/platform-app-yaml.html

# The name of this app. Must be unique within a project.
name: app

# The runtime the application uses.
type: nodejs:22

# Set the timezone to Europe/Paris
timezone: "Europe/Paris"

# The size of the persistent disk of the application (in MB).
disk: 5000

resources:
  base_memory: 512
  memory_ratio: 512

# The hooks executed at various points in the lifecycle of the application.
hooks:
  build: npm run build:prod

# The configuration of app when it is exposed to the web..
web:
  locations:
    /:
      # Static site generators usually output built static files to a specific directory.
      # Define this directory (must be an actual directory inside the root directory of your app)
      # as the root for your static site.
      root: "dist/premium/browser"
      # Files to consider when serving a request for a directory.
      index:
        - index.csr.html
      # Forward resources to the app.
      passthru: "/index.csr.html"
      # Allow files even without specified rules.
      allow: true
