pb_flag.post:
  path: '/api/post.json'
  defaults:
    _controller: '\Drupal\pb_flag\Controller\PbFlagController::post'
  methods: [POST]
  requirements:
    _permission: 'access content'
pb_flag.delete:
  path: '/api/delete.json'
  defaults:
    _controller: '\Drupal\pb_flag\Controller\PbFlagController::delete'
  methods: [DELETE]
  requirements:
    _permission: 'access content'
pb_flag.get:
  path: '/api/gfuzs&45ah_iefwhzHED9&H-GH/girl-des-tages.json'
  defaults:
    _controller: '\Drupal\pb_flag\Controller\PbFlagController::girlDesTages'
  methods: [GET]
  requirements:
    _access: 'TRUE'