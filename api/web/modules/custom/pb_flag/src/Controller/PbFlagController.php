<?php

/**
 * @file
 * Contains \Drupal\pb_flag\Controller\PbFlagController.
 */

namespace Drupal\pb_flag\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Drupal\user\Entity\User;
use Drupal\file\Entity\File;

/**
 * Controller routines for pb_flag routes.
 */
class PbFlagController extends ControllerBase
{

  /**
   * Callback for `api/post.json` API method.
   */
  public function post(Request $request)
  {

    // This condition checks the `Content-type` and makes sure to 
    // decode JSON string from the request body into array.
    if (0 === strpos($request->headers->get('Content-Type'), 'application/json')) {
      $data = json_decode($request->getContent(), TRUE);
      $request->request->replace(is_array($data) ? $data : []);
    }


    //get value json
    $flag_id = $data["flag_id"];
    $entity_type = $data["entity_type"];
    $entity_id = $data["entity_id"];
    $uid = $data["uid"];
    $created = REQUEST_TIME;

    //get user   
    $user = User::load($uid);

    //get field uuid
    $user_uuid = $user->get('uuid')->value;

    //add entity_id to the field field uuid (unique value);
    $user_uuid = $user_uuid . '-' . $data["entity_id"];

    $fields = array(
      'flag_id' => $flag_id,
      'uuid' => $user_uuid, //unique value
      'entity_type' => $entity_type,
      'entity_id' => $entity_id,
      'uid' => $uid,
      'global' => 0,
      'created' => $created
    );


    if ($data["flag_action"] == 'flag') {

      //verify if exists record
      $sql = "select * from {flagging} where entity_type ='$entity_type' and entity_id=$entity_id and flag_id='$flag_id' and uid=$uid ";
      //execute query
      $connection = \Drupal::database();
      $result = $connection->query($sql);
      $result = $result->fetchAll();

      //if not exists record, create flag
      if (count($result) > 0) {
        $response['¿Flag exists?'] = 'true';
      } else {

        $resultado = db_insert('flagging')
          ->fields($fields)
          ->execute();

        $response['newflag'] = $resultado;
      }


      $response['data'] = $data;
    } else if ($data["flag_action"] == 'unflag') {

      //verify if exits record
      $sql = "select * from {flagging} where entity_type ='$entity_type' and entity_id=$entity_id and flag_id='$flag_id' and uid=$uid ";
      //execute query
      $connection = \Drupal::database();
      $result = $connection->query($sql);
      $result = $result->fetchAll();

      //if exists record, delete flag
      if (count($result) > 0) {

        $num_deleted = db_delete('flagging')
          ->condition('entity_type', $entity_type)
          ->condition('entity_id', $entity_id)
          ->condition('flag_id', $flag_id)
          ->condition('uid', $uid)
          ->execute();

        $response['¿Delete flag?'] = 'true';
      } else {

        $response['¿Flag exists?'] = 'false';
      }
      $response['data'] = $data;
    }


    /*

    //send json example
    //change "bookmark"  by "my_flag"


    {
      "entity_id":31,
      "entity_type":"node",
      "flag_id": "bookmark", 
      "uid": 1,
      "flag_action": "flag"
    }

    */



    return new JsonResponse($response);
  }

  /**
   * Callback for `api/delete.json` API method.
   */
  public function delete(Request $request)
  {

    $response['data'] = 'Some test data to return';
    $response['method'] = 'DELETE';

    return new JsonResponse($response);
  }

  /**
   * Callback for `api/gfuzs&45ah_iefwhzHED9&H-GH/girl-des-tages.json` API method.
   */



  public function girlDesTages(Request $request)
  {

    $itemsPerPage = 50;
    $page = !is_null($request->get('page')) ? intval($request->get('page')) : 0;
    
    $query = \Drupal::entityQuery('girl_info');
    $query->condition('status', 1);
    $query->condition('field_export_girl_des_tages', true);
    $query->range($page * $itemsPerPage, $itemsPerPage);
    $ids = $query->execute();

    $storage = \Drupal::entityTypeManager()->getStorage('girl_info');
    $response = $storage->loadMultiple($ids);


    $data = [];
    json_encode($data);
    $signController = new \Drupal\pb_token\Controller\PbTokenController();
    $baseUrl = $signController->getToken();

    foreach ($response as $girlInfo) {

      $category = $girlInfo->get('field_category')->referencedEntities();
      if (is_null($category) || !is_array($category)) {
        $category = '';
      } else {
        $category = $category[0]->getName();
      }

      $descriptorCountry = $girlInfo->get('descriptor_country_ref')->referencedEntities();
      if (!is_null($descriptorCountry) && is_array($descriptorCountry) && !empty($descriptorCountry)) {
        $descriptorCountry = $descriptorCountry[0]->getName();
      }

      $girlInfoData = [];
      $girlInfoData['id'] = $girlInfo->get('id')->value;
      $girlInfoData['name'] = $girlInfo->get('name')->value;
      $girlInfoData['category'] = $category;
      $girlInfoData['descriptorCountry'] = $descriptorCountry;
      $girlInfoData['descriptorMonth'] = $girlInfo->get('descriptor_month')->value;
      $girlInfoData['descriptorYear'] = $girlInfo->get('descriptor_year')->value;
    
      $imagesData = [];

      /* Girl Info Main Image */
      $mainImage = $girlInfo->get('main_images')->referencedEntities();
      if (!is_null($mainImage) && is_array($mainImage)  && !empty($mainImage)) {
        $mainImageFile = File::load($mainImage[0]->get('field_media_image')->target_id);
        $mainImageUrl = $mainImageFile->getFileUri();
        $mainTrimUrl = substr($mainImageUrl, strpos($mainImageUrl, "//") + 2); 
        $mainFinalUrl = $baseUrl . $mainTrimUrl;

        /* Girl Info Main Image Focal Point */
        $focalX = $mainImage[0]->get('field_focal_point_x')->value;
        $focalY = $mainImage[0]->get('field_focal_point_y')->value;
        $focalXtext = 'Center';
        $focalYtext = 'center';

        if ($focalX > 70) {
          $focalXtext = 'Right';
        } elseif ($focalX < 30) {
          $focalXtext = 'Left';
        }

        if ($focalY < 70) {
          $focalYtext = 'top';
        } elseif ($focalY > 30) {
          $focalYtext = 'bottom';
        }
        $focalPoint = $focalYtext . $focalXtext;
        $girlInfoData['focalPoint'] = $focalPoint;
        array_push($imagesData, $mainFinalUrl);
      }

      /* Girl Info Gallery Images */
      $galleries = $girlInfo->get('galleries')->referencedEntities();
      if (!is_null($galleries) && !empty($galleries) && is_array($galleries)) {
        $gallery = $galleries[0];
        $images = $gallery->get('field_media_slideshow')->referencedEntities();
        if (!is_null($images) && !empty($images) && is_array($images)) {
          foreach ($images as $image) {
            $file = File::load($image->get('field_media_image')->target_id);
            if (!is_null($file)){
              $url = $file->getFileUri();
              $trimUrl = substr($url, strpos($url, "//") + 2); 
              $finalUrl = $baseUrl . $trimUrl;
              array_push($imagesData, $finalUrl);
            }
          }
        }
      }
      /* First 9 Girl Info Gallery Images */
      $girlInfoData['images'] = array_slice($imagesData, 0, 9);
      array_push($data, $girlInfoData);
    }

    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data); 
    die();
  }
}