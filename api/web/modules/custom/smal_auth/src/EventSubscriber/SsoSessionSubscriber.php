<?php

namespace Drupal\smal_auth\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Drupal\Core\Session\AccountProxyInterface;
use Drupal\Core\Cache\CacheBackendInterface;
use <PERSON>upal\smal_auth\Sso\SsoClient;
use Drupal\Core\Session\SessionManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Drupal\Core\Session\AccountInterface;


class SsoSessionSubscriber implements EventSubscriberInterface
{

  protected $currentUser;
  protected $cache;
  protected $ssoClient;
  protected $sessionManager;
  protected $requestStack;



  public function __construct(AccountProxyInterface $current_user, CacheBackendInterface $cache, SsoClient $ssoClient, SessionManagerInterface $session_manager, RequestStack $request_stack)
  {
    $this->currentUser = $current_user;
    $this->cache = $cache;
    $this->ssoClient = $ssoClient;
    $this->sessionManager = $session_manager;
    $this->requestStack = $request_stack;
  }

  public function onKernelRequest(RequestEvent $event)
  {
    // Only check on master requests and for logged-in users
    if (!$event->isMasterRequest()) {
      return;
    }

    \Drupal::logger('smal_auth')->warning("Session checking started");

    $roles = $this->currentUser->getRoles();

    \Drupal::logger('smal_auth')->warning("Session checking started for roles: " . implode(', ', $roles));

    if (in_array('administrator', $roles)) {
      $uid = $this->currentUser->id();
      $cache_id = "sso_check:$uid";
      $cached = $this->cache->get($cache_id);

      // Check if the cache is empty or expired
      if (empty($cached)) {
        \Drupal::logger('smal_auth')->warning("Cache is empty or expired for user $uid");
      } else {
        \Drupal::logger('smal_auth')->warning("Cache is valid for user $uid");
      }

      if (!$cached || (\Drupal::time()->getRequestTime() - $cached->created) > 3600) {
        $request = $event->getRequest();
        $is_valid = $this->ssoClient->checkSession($request);

        \Drupal::logger('smal_auth')->warning("SSO session check result: " . ($is_valid ? 'valid' : 'invalid'));
        if ($is_valid) {
          \Drupal::logger('smal_auth')->warning("User $uid is still logged in.");
        } else {
          \Drupal::logger('smal_auth')->warning("User $uid is not logged in.");
        }

        if (!$is_valid) {
          \Drupal::logger('smal_auth')->warning("Logging out user $uid due to invalid SSO session.");

          // Log the user out
          $this->sessionManager->destroy();

          // Redirect back to the current URL
          $current_url = $this->requestStack->getCurrentRequest()->getUri();
          $event->setResponse(new RedirectResponse($current_url));
        }

        $this->cache->set($cache_id, ['valid' => $is_valid], time() + 3600);
      }
    }
  }

  public static function getSubscribedEvents()
  {
    return [
      KernelEvents::REQUEST => ['onKernelRequest', 100],
    ];
  }
}
