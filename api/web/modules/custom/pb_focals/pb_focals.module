<?php

use <PERSON><PERSON>al\Core\Form\FormStateInterface;




/**
 * Implements hook_form_media_form_alter().
 */
function pb_focals_media_library_form_alter(&$form, FormStateInterface $form_state, $form_id)
{
  $form['pb_focals'] = [
    '#attached' => [
      'library' => [
        'pb_focals/drupal.pb_focals.admin',
      ],
    ],
  ];
}
function pb_focals_form_media_form_alter(&$form, FormStateInterface $form_state, $form_id)
{



  $media = $form_state->getFormObject()->getEntity();
  $media_type = $media->bundle();

  // only add form to s_product content type
  if ($media_type == 'image' || $media_type == 'gallery') {

    // Add the list to the vertical tabs section of the form.
    $form['pb_focals'] = [
      '#attached' => [
        'library' => [
          'pb_focals/drupal.pb_focals.admin',
        ],
      ],
    ];
  }
}
/**
 * Implements hook_form_media_form_alter().
 */
function pb_focals_form_girl_info_form_alter(&$form, FormStateInterface $form_state, $form_id)
{

  // Add the list to the vertical tabs section of the form.
  $form['pb_focals'] = [
    '#attached' => [
      'library' => [
        'pb_focals/drupal.pb_focals.admin',
      ],
    ],
  ];
}