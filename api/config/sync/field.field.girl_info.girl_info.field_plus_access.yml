uuid: 67ca2b7e-8526-4743-9df3-a1e598012af8
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_plus_access
  module:
    - pb_girl_info
id: girl_info.girl_info.field_plus_access
field_name: field_plus_access
entity_type: girl_info
bundle: girl_info
label: 'Plus Access'
description: 'Darf nicht manuell geändert werden. Wird automatisch gefüllt. Händisch ausgewählte Girl Infos für Plus User müssen unter Content "Plus Galerien" ausgewählt werden.'
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: 'On'
  off_label: 'Off'
field_type: boolean
