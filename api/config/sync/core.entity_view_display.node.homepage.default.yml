uuid: 8a1e51f6-ba5a-4f5b-a018-e1bf9758c16e
langcode: en
status: true
dependencies:
  config:
    - field.field.node.homepage.field_banner_price
    - field.field.node.homepage.field_banner_title
    - field.field.node.homepage.field_cover
    - field.field.node.homepage.field_featured_video
    - field.field.node.homepage.field_features
    - field.field.node.homepage.field_grid_subtitle1
    - field.field.node.homepage.field_grid_subtitle2
    - field.field.node.homepage.field_grid_subtitle3
    - field.field.node.homepage.field_grid_title1
    - field.field.node.homepage.field_grid_title2
    - field.field.node.homepage.field_grid_title3
    - field.field.node.homepage.field_hero_slideshow
    - field.field.node.homepage.field_maintenance_mode
    - field.field.node.homepage.field_slideshow
    - field.field.node.homepage.field_subtitle
    - field.field.node.homepage.field_title
    - field.field.node.homepage.field_video_title
    - node.type.homepage
  module:
    - entity_reference_revisions
    - svg_image
    - user
id: node.homepage.default
targetEntityType: node
bundle: homepage
mode: default
content:
  content_moderation_control:
    settings: {  }
    third_party_settings: {  }
    weight: -20
    region: content
  field_banner_price:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 112
    region: content
  field_banner_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 111
    region: content
  field_cover:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 101
    region: content
  field_featured_video:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 114
    region: content
  field_features:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 116
    region: content
  field_grid_subtitle1:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 105
    region: content
  field_grid_subtitle2:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 106
    region: content
  field_grid_subtitle3:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 107
    region: content
  field_grid_title1:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 108
    region: content
  field_grid_title2:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 109
    region: content
  field_grid_title3:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 110
    region: content
  field_hero_slideshow:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 102
    region: content
  field_maintenance_mode:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 117
    region: content
  field_slideshow:
    type: image
    label: above
    settings:
      image_link: ''
      image_style: ''
    third_party_settings: {  }
    weight: 115
    region: content
  field_subtitle:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 104
    region: content
  field_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 103
    region: content
  field_video_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 113
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  langcode: true
  search_api_excerpt: true
