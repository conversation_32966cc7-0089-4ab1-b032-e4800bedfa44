uuid: 9bacca0c-17e5-4c2d-80b4-b6511ae55e90
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_new_p
    - paragraphs.paragraphs_type.coupon_card
id: paragraph.coupon_card.field_new_p
field_name: field_new_p
entity_type: paragraph
bundle: coupon_card
label: 'New Price'
description: 'Der rabattierte Preis für die gesamte Laufzeit'
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
