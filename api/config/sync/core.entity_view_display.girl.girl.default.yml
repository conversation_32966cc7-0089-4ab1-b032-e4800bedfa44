uuid: 06c03782-de76-4b0a-ad58-03dcba87671b
langcode: en
status: true
dependencies:
  config:
    - field.field.girl.girl.field_from
    - field.field.girl.girl.field_notes
    - field.field.girl.girl.field_release_date
    - field.field.girl.girl.field_to
  module:
    - datetime
    - pb_girl
    - text
id: girl.girl.default
targetEntityType: girl
bundle: girl
mode: default
content:
  field_from:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 11
    region: content
  field_notes:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 14
    region: content
  field_release_date:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 13
    region: content
  field_to:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 12
    region: content
  firstname:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -4
    region: content
  flag_girl_flag:
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: content
  girl_infos:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
  images:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -4
    region: content
  lastname:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -4
    region: content
  middlename:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -4
    region: content
  name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -4
    region: content
hidden:
  langcode: true
  main_images: true
  non_nude_images: true
  search_api_excerpt: true
