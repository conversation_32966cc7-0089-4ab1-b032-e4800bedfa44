uuid: e1beff0b-28f4-4c84-aa89-8c28b186ff3d
langcode: en
status: true
dependencies:
  config:
    - field.field.node.modular_page.field_modules
    - field.field.node.modular_page.field_url
    - node.type.modular_page
  module:
    - content_moderation
    - paragraphs
    - path
    - scheduler
    - scheduler_content_moderation_integration
id: node.modular_page.default
targetEntityType: node
bundle: modular_page
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_modules:
    type: entity_reference_paragraphs
    weight: 14
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: closed
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
    third_party_settings: {  }
  field_url:
    type: string_textfield
    weight: 13
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_state:
    type: scheduler_moderation
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 12
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_state:
    type: scheduler_moderation
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
