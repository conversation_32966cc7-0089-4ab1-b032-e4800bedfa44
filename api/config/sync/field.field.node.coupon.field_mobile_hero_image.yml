uuid: 1f9bf5ca-dd64-4b21-a12c-294191146a88
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_mobile_hero_image
    - node.type.coupon
  module:
    - image
id: node.coupon.field_mobile_hero_image
field_name: field_mobile_hero_image
entity_type: node
bundle: coupon
label: 'Mobile Hero Image'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: false
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
