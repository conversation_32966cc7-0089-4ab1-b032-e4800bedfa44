uuid: 8efc7432-1331-42e0-96de-4842e15aef2a
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.coupon_card.field_coupon
    - field.field.paragraph.coupon_card.field_month
    - field.field.paragraph.coupon_card.field_new_p
    - field.field.paragraph.coupon_card.field_old_price
    - paragraphs.paragraphs_type.coupon_card
  module:
    - options
id: paragraph.coupon_card.default
targetEntityType: paragraph
bundle: coupon_card
mode: default
content:
  field_coupon:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 4
    region: content
  field_month:
    type: list_key
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  field_new_p:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_old_price:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 2
    region: content
hidden:
  search_api_excerpt: true
