uuid: 5081ebac-8e8c-4a95-885a-3cb7b575076b
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_notice
    - node.type.coupon
  module:
    - text
id: node.coupon.field_notice
field_name: field_notice
entity_type: node
bundle: coupon
label: Notice
description: 'Bitte Abo-Arten und Gültigkeit des Gutscheins entsprechend anpassen. '
required: false
translatable: false
default_value:
  -
    value: "<p>1) alle Preise inkl. gesetzlich gültiger MwSt.<br />\r\n<br />\r\n2) Der PlayboyPremium Gutschein berechtigt Sie zur Nutzung von PlayboyPremium für die gewählte Laufzeit. Die gewählte Laufzeit für PlayboyPremium beginnt in jedem Fall erst mit Einlösung des Gutscheins.</p>\r\n\r\n<p>3) Die Vertragslaufzeit von PlayboyPremium für Kunden steht in Abhängigkeit von der gewählten Abo-Art. Kunden können zwischen 1, 3, 6 und 12 Monaten wählen. Der Vertrag verlängert sich automatisch auf unbestimmte Zeit wenn er nicht mit Frist von einem Monat vor Ablauf der zunächst vorgesehenen Vertragsdauer gekündigt wird.</p>\r\n\r\n<p>4) Das Abonnement ist jederzeit zum Ende der Mindestlaufzeit oder danach mit einer Frist von einem Monat in Textform oder über Ihr persönliches Profil kündbar. PlayboyPremium kann im Anschluss bis zum vertraglich vorhandenen Zeitraumende weiter genutzt werden. Maßgebend ist der Zeitpunkt des Zugangs der Kündigungserklärung.</p>\r\n"
    format: basic_html
default_value_callback: ''
settings: {  }
field_type: text_long
