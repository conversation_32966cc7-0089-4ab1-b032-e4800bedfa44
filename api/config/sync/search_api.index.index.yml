uuid: 5986fedc-d98a-416a-a81c-1f5c0008aedb
langcode: en
status: true
dependencies:
  config:
    - search_api.server.solr
  module:
    - search_api_solr
    - pb_girl_info
    - taxonomy
    - pb_girl
    - search_api
third_party_settings:
  search_api_solr:
    finalize: false
    commit_before_finalize: false
    commit_after_finalize: false
    highlighter:
      maxAnalyzedChars: 51200
      fragmenter: gap
      usePhraseHighlighter: true
      highlightMultiTerm: true
      preserveMulti: false
      regex:
        slop: 0.5
        pattern: blank
        maxAnalyzedChars: 10000
      highlight:
        mergeContiguous: false
        requireFieldMatch: false
        snippets: 3
        fragsize: 0
    mlt:
      mintf: 1
      mindf: 1
      maxdf: 0
      maxdfpct: 0
      minwl: 0
      maxwl: 0
      maxqt: 100
      maxntp: 2000
      boost: false
      interestingTerms: none
    term_modifiers:
      slop: 3
      fuzzy: 1
    advanced:
      index_prefix: ''
      collection: ''
      timezone: ''
    multilingual:
      limit_to_content_language: false
      include_language_independent: true
id: index
name: Index
description: ''
read_only: false
field_settings:
  category:
    label: 'Descriptor category/title » Taxonomy term » Name'
    datasource_id: 'entity:girl_info'
    property_path: 'descriptor_category:entity:name'
    type: 'solr_text_custom:ngram'
    dependencies:
      module:
        - pb_girl_info
        - taxonomy
  city:
    label: 'City reference » Taxonomy term » Name'
    datasource_id: 'entity:girl_info'
    property_path: 'city:entity:name'
    type: 'solr_text_custom:ngram'
    boost: 0.5
    dependencies:
      module:
        - pb_girl_info
        - taxonomy
  country:
    label: 'Country reference » Taxonomy term » Name'
    datasource_id: 'entity:girl_info'
    property_path: 'country:entity:name'
    type: 'solr_text_custom:ngram'
    boost: 0.5
    dependencies:
      module:
        - pb_girl_info
        - taxonomy
  eyecolor:
    label: 'Eyecolor reference » Taxonomy term » Name'
    datasource_id: 'entity:girl_info'
    property_path: 'eyecolor:entity:name'
    type: 'solr_text_custom:ngram'
    dependencies:
      module:
        - pb_girl_info
        - taxonomy
  firstname:
    label: Firstname
    datasource_id: 'entity:girl_info'
    property_path: firstname
    type: 'solr_text_custom:ngram'
    boost: !!float 2
    dependencies:
      module:
        - pb_girl_info
  girl:
    label: 'Girl » Girl » ID'
    datasource_id: 'entity:girl_info'
    property_path: 'girl:entity:id'
    type: integer
    dependencies:
      module:
        - pb_girl
        - pb_girl_info
  haircolor:
    label: 'Haircolor reference » Taxonomy term » Name'
    datasource_id: 'entity:girl_info'
    property_path: 'haircolor:entity:name'
    type: 'solr_text_custom:ngram'
    dependencies:
      module:
        - pb_girl_info
        - taxonomy
  id:
    label: ID
    datasource_id: 'entity:girl_info'
    property_path: id
    type: integer
    dependencies:
      module:
        - pb_girl_info
  lastname:
    label: Lastname
    datasource_id: 'entity:girl_info'
    property_path: lastname
    type: 'solr_text_custom:ngram'
    boost: !!float 2
    dependencies:
      module:
        - pb_girl_info
  name:
    label: Name
    datasource_id: 'entity:girl_info'
    property_path: name
    type: 'solr_text_custom:ngram'
    boost: !!float 2
    dependencies:
      module:
        - pb_girl_info
  tags:
    label: 'Tags » Taxonomy term » Name'
    datasource_id: 'entity:girl_info'
    property_path: 'tags:entity:name'
    type: string
    dependencies:
      module:
        - pb_girl_info
        - taxonomy
datasource_settings:
  'entity:girl_info':
    languages:
      default: true
      selected: {  }
processor_settings:
  add_url: {  }
  aggregated_field: {  }
  entity_status: {  }
  language_with_fallback: {  }
  rendered_item: {  }
  solr_date_range:
    weights:
      preprocess_index: 0
tracker_settings:
  default:
    indexing_order: fifo
options:
  cron_limit: 50
  index_directly: true
  track_changes_in_references: true
server: solr
