uuid: cb8ddaa5-26ba-43c7-bdee-2b1713e0349e
langcode: en
status: true
dependencies: {  }
_core:
  default_config_hash: ESG2x3MSLc7DomtiTVDA4Ye7Bsz0Og_LE1C1h6RQwx0
id: pb_girls_nn_image
class: null
field_plugin_method: null
cck_plugin_method: null
migration_tags: null
migration_group: pb_girls_image
label: 'Import girl non-nude image entities'
source:
  plugin: url
  glob: true
  urls:
    - 'public://migration/girl_info/processed/girls/*.json'
  data_fetcher_plugin: file
  data_parser_plugin: json
  item_selector: girls
  fields:
    -
      name: girl_image_id
      label: 'Primary girl info id'
      selector: mainImage/nonNude/id
    -
      name: uuid
      label: Uuid
      selector: mainImage/nonNude/uuid
    -
      name: description
      label: description
      selector: mainImage/nonNude/description
    -
      name: credit
      label: credit
      selector: mainImage/nonNude/credit
    -
      name: image
      label: Image
      selector: mainImage/nonNude/file
  ids:
    uuid:
      type: string
  constants:
    file_destination: 'private://'
process:
  mid: girl_image_id
  uuid: uuid
  label: girl_image_id
  field_description: description
  field_credit: credit
  type:
    plugin: default_value
    default_value: image
  _file_destination:
    plugin: concat
    source:
      - constants/file_destination
      - image
  field_media_image:
    plugin: file_remote_image
    source: '@_file_destination'
destination:
  plugin: 'entity:media'
  default_bundle: image
migration_dependencies: {  }
