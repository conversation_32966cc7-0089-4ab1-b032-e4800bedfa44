uuid: 67cde8c7-667e-4dce-bad0-011181912da2
langcode: en
status: true
dependencies:
  config:
    - field.field.taxonomy_term.category.field_image
    - field.field.taxonomy_term.category.field_link
    - taxonomy.vocabulary.category
  module:
    - field_group
    - link
    - media_library
    - path
    - text
third_party_settings:
  field_group:
    group_modular_page:
      children:
        - field_image
        - field_link
      label: 'Modular Page'
      region: content
      parent_name: ''
      weight: 5
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        open: true
        description: ''
        required_fields: true
id: taxonomy_term.category.default
targetEntityType: taxonomy_term
bundle: category
mode: default
content:
  description:
    type: text_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_image:
    type: media_library_widget
    weight: 6
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_link:
    type: link_default
    weight: 7
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden: {  }
