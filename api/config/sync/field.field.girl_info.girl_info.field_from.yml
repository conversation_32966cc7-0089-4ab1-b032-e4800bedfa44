uuid: 950a35e6-72f6-45e2-8333-1e604dacfa2e
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_from
  module:
    - datetime
    - pb_girl_info
id: girl_info.girl_info.field_from
field_name: field_from
entity_type: girl_info
bundle: girl_info
label: From
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: datetime
