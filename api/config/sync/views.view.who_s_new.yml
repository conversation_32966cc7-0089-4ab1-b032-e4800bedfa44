uuid: 5ae5eecb-8af8-4b28-bd97-a01993b58741
langcode: en
status: false
dependencies:
  module:
    - user
_core:
  default_config_hash: hdAKtAm0H-umA4Nzn0i1bquaj5iPn3daD_7u-nzFRhM
id: who_s_new
label: 'Who''s new'
module: user
description: 'Shows a list of the newest user accounts on the site.'
tag: default
base_table: users_field_data
base_field: uid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Who''s new'
      fields:
        name:
          id: name
          table: users_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: name
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: user_name
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 5
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: users_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: created
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        status:
          id: status
          table: users_field_data
          field: status
          entity_type: user
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: '0'
            operator_limit_selection: false
            operator_list: {  }
        access:
          id: access
          table: users_field_data
          field: access
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: access
          plugin_id: date
          operator: '>'
          value:
            min: ''
            max: ''
            value: '1970-01-01'
            type: date
          group: 1
          exposed: false
          expose:
            operator_id: '0'
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: html_list
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
  block_1:
    id: block_1
    display_title: 'Who''s new'
    display_plugin: block
    position: 1
    display_options:
      display_description: 'A list of new users'
      display_extenders: {  }
      block_description: 'Who''s new'
      block_category: User
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
