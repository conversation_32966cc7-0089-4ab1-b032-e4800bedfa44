uuid: 09cb1586-fc2e-44da-a161-c4e643b2c034
langcode: en
status: true
dependencies:
  config:
    - field.field.media.image.field_credit
    - field.field.media.image.field_description
    - field.field.media.image.field_focal_point_x
    - field.field.media.image.field_focal_point_y
    - field.field.media.image.field_fsk
    - field.field.media.image.field_media_image
    - image.style.thumbnail
    - media.type.image
  module:
    - image
    - text
_core:
  default_config_hash: WeM0d1VqhuyGELo4SRiDJfhDQSLqfKavISEbrnlcTaE
id: media.image.default
targetEntityType: media
bundle: image
mode: default
content:
  field_credit:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_description:
    type: text_textarea
    weight: 3
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_focal_point_x:
    type: string_textfield
    weight: 27
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_focal_point_y:
    type: string_textfield
    weight: 28
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_fsk:
    type: options_select
    weight: 26
    region: content
    settings: {  }
    third_party_settings: {  }
  field_media_image:
    type: image_image
    weight: 0
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
hidden:
  created: true
  name: true
  path: true
  status: true
  uid: true
