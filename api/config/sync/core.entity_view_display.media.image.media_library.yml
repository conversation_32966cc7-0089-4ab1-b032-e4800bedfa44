uuid: 1064c37c-78bd-4541-9ef1-e4d5158fe014
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.media_library
    - field.field.media.image.field_credit
    - field.field.media.image.field_description
    - field.field.media.image.field_focal_point_x
    - field.field.media.image.field_focal_point_y
    - field.field.media.image.field_fsk
    - field.field.media.image.field_media_image
    - image.style.medium
    - media.type.image
  module:
    - image
_core:
  default_config_hash: PaGXvzRcL9eII--JV4eCVfObjrNo0l-u1dB_WJtB9ig
id: media.image.media_library
targetEntityType: media
bundle: image
mode: media_library
content:
  flag_image_flag:
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: content
  thumbnail:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: medium
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  created: true
  field_credit: true
  field_description: true
  field_focal_point_x: true
  field_focal_point_y: true
  field_fsk: true
  field_media_image: true
  langcode: true
  name: true
  search_api_excerpt: true
  uid: true
