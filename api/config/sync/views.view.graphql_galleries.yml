uuid: 86ef020b-6364-4be9-85d5-8f2bdbe04111
langcode: en
status: true
dependencies:
  config:
    - media.type.gallery
    - taxonomy.vocabulary.category
    - taxonomy.vocabulary.preferences
    - user.role.administrator
  module:
    - datetime
    - graphql_views
    - media
    - pb_girl
    - pb_girl_info
    - statistics
    - taxonomy
    - user
id: graphql_galleries
label: 'GraphQL / Galleries'
module: views
description: ''
tag: ''
base_table: media_field_data
base_field: mid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        mid:
          id: mid
          table: media_field_data
          field: mid
          relationship: none
          group_type: count
          admin_label: ''
          entity_type: media
          entity_field: mid
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings: {  }
          group_column: entity_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ''
          field_api_classes: false
          set_precision: false
          precision: 0
          decimal: .
          format_plural: 0
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
      cache:
        type: time
        options:
          results_lifespan: 3600
          results_lifespan_custom: 0
          output_lifespan: 3600
          output_lifespan_custom: 0
      empty: {  }
      sorts:
        field_publish_date_value:
          id: field_publish_date_value
          table: media__field_publish_date
          field: field_publish_date_value
          relationship: none
          group_type: max
          admin_label: ''
          plugin_id: datetime
          order: DESC
          expose:
            label: 'Publish date (field_publish_date)'
            field_identifier: field_publish_date_value
          exposed: true
          granularity: second
        daycount:
          id: daycount
          table: girl_info_counter
          field: daycount
          relationship: reverse__girl_info__galleries
          group_type: max
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: 'Views today'
            field_identifier: daycount
          exposed: true
        totalcount:
          id: totalcount
          table: girl_info_counter
          field: totalcount
          relationship: reverse__girl_info__galleries
          group_type: max
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: 'Total views'
            field_identifier: totalcount
          exposed: true
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: id
          exposed: false
      arguments: {  }
      filters:
        status:
          id: status
          table: media_field_data
          field: status
          entity_type: media
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        status_1:
          id: status_1
          table: girl_field_data
          field: status
          relationship: girl
          group_type: group
          admin_label: ''
          entity_type: girl
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status_2:
          id: status_2
          table: girl_info_field_data
          field: status
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        bundle:
          id: bundle
          table: media_field_data
          field: bundle
          entity_type: media
          entity_field: bundle
          plugin_id: bundle
          value:
            gallery: gallery
          group: 1
          expose:
            operator_limit_selection: false
            operator_list: {  }
        id:
          id: id
          table: girl_field_data
          field: id
          relationship: girl
          group_type: group
          admin_label: ''
          entity_type: girl
          entity_field: id
          plugin_id: numeric
          operator: regular_expression
          value:
            min: ''
            max: ''
            value: ''
          group: 2
          exposed: true
          expose:
            operator_id: id_op
            label: ID
            description: ''
            use_operator: false
            operator: id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: girl_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        id_1:
          id: id_1
          table: girl_info_field_data
          field: id
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: numeric
          operator: regular_expression
          value:
            min: ''
            max: ''
            value: ''
          group: 2
          exposed: true
          expose:
            operator_id: id_1_op
            label: ID
            description: ''
            use_operator: false
            operator: id_1_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: girl_info_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_publish_date_value_2:
          id: field_publish_date_value_2
          table: media__field_publish_date
          field: field_publish_date_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>'
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 3
          exposed: true
          expose:
            operator_id: field_publish_date_value_2_op
            label: 'Publish date (field_publish_date)'
            description: ''
            use_operator: false
            operator: field_publish_date_value_2_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: publish_date
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_country_ref:
          id: descriptor_country_ref
          table: girl_info_field_data
          field: descriptor_country_ref
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_country_ref
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 3
          exposed: true
          expose:
            operator_id: descriptor_country_ref_op
            label: 'Descriptor country reference'
            description: ''
            use_operator: false
            operator: descriptor_country_ref_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: country_is
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_country_ref_1:
          id: descriptor_country_ref_1
          table: girl_info_field_data
          field: descriptor_country_ref
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_country_ref
          plugin_id: numeric
          operator: '!='
          value:
            min: ''
            max: ''
            value: ''
          group: 3
          exposed: true
          expose:
            operator_id: descriptor_country_ref_1_op
            label: 'Descriptor country reference'
            description: ''
            use_operator: false
            operator: descriptor_country_ref_1_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: country_is_not
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_year:
          id: descriptor_year
          table: girl_info_field_data
          field: descriptor_year
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_year
          plugin_id: numeric
          operator: between
          value:
            min: ''
            max: ''
            value: ''
          group: 3
          exposed: true
          expose:
            operator_id: descriptor_year_op
            label: 'Descriptor year'
            description: ''
            use_operator: false
            operator: descriptor_year_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: descriptor_year
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_release_date_value:
          id: field_release_date_value
          table: media__field_release_date
          field: field_release_date_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: between
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 3
          exposed: true
          expose:
            operator_id: field_release_date_value_op
            label: 'Release Date (field_release_date)'
            description: ''
            use_operator: false
            operator: field_release_date_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_release_date_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        haircolor:
          id: haircolor
          table: girl_info_field_data
          field: haircolor
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: haircolor
          plugin_id: numeric
          operator: regular_expression
          value:
            min: ''
            max: ''
            value: ''
          group: 3
          exposed: true
          expose:
            operator_id: haircolor_op
            label: 'Haircolor reference'
            description: ''
            use_operator: false
            operator: haircolor_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: haircolor
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        eyecolor:
          id: eyecolor
          table: girl_info_field_data
          field: eyecolor
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: eyecolor
          plugin_id: numeric
          operator: regular_expression
          value:
            min: ''
            max: ''
            value: ''
          group: 3
          exposed: true
          expose:
            operator_id: eyecolor_op
            label: 'Eyecolor reference'
            description: ''
            use_operator: false
            operator: eyecolor_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: eyecolor
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        bustsize:
          id: bustsize
          table: girl_info_field_data
          field: bustsize
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: bustsize
          plugin_id: numeric
          operator: between
          value:
            min: ''
            max: ''
            value: ''
          group: 3
          exposed: true
          expose:
            operator_id: bustsize_op
            label: Bustsize
            description: ''
            use_operator: false
            operator: bustsize_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: bustsize
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_category_target_id:
          id: field_category_target_id
          table: girl_info__field_category
          field: field_category_target_id
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 3
          exposed: true
          expose:
            operator_id: field_category_target_id_op
            label: 'Category (field_category)'
            description: ''
            use_operator: false
            operator: field_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_category
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: category
          type: select
          hierarchy: true
          limit: true
          error_message: true
        field_preference_target_id:
          id: field_preference_target_id
          table: taxonomy_term__field_preference
          field: field_preference_target_id
          relationship: field_tags
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 3
          exposed: true
          expose:
            operator_id: field_preference_target_id_op
            label: Preference
            description: ''
            use_operator: false
            operator: field_preference_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_preference_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: preferences
          type: textfield
          hierarchy: false
          limit: true
          error_message: true
        description:
          id: description
          table: girl_info_field_data
          field: description
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: description
          plugin_id: string
          operator: allwords
          value: ''
          group: 4
          exposed: true
          expose:
            operator_id: description_op
            label: Description
            description: ''
            use_operator: false
            operator: description_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: search_description
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        name:
          id: name
          table: girl_info_field_data
          field: name
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: name
          plugin_id: string
          operator: allwords
          value: ''
          group: 4
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: search_name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_credit_value:
          id: field_credit_value
          table: media__field_credit
          field: field_credit_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: allwords
          value: ''
          group: 4
          exposed: true
          expose:
            operator_id: field_credit_value_op
            label: 'Credit (field_credit)'
            description: ''
            use_operator: false
            operator: field_credit_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: search_credit
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        name_1:
          id: name_1
          table: taxonomy_term_field_data
          field: name
          relationship: field_tags
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          entity_field: name
          plugin_id: string
          operator: allwords
          value: ''
          group: 4
          exposed: true
          expose:
            operator_id: name_1_op
            label: Name
            description: ''
            use_operator: false
            operator: name_1_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: tags
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
          3: AND
          4: OR
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        reverse__girl_info__galleries:
          id: reverse__girl_info__galleries
          table: media_field_data
          field: reverse__girl_info__galleries
          relationship: none
          group_type: group
          admin_label: galleries
          entity_type: media
          plugin_id: entity_reverse
          required: true
        girl:
          id: girl
          table: girl_info_field_data
          field: girl
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: Girl
          entity_type: girl_info
          entity_field: girl
          plugin_id: standard
          required: true
        field_tags:
          id: field_tags
          table: girl_info__field_tags
          field: field_tags
          relationship: reverse__girl_info__galleries
          group_type: group
          admin_label: 'field_tags: Taxonomy term'
          plugin_id: standard
          required: false
      group_by: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user
        - user.roles
      tags: {  }
  graphql_1:
    id: graphql_1
    display_title: GraphQL
    display_plugin: graphql
    position: 1
    display_options:
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      style:
        type: graphql
        options:
          uses_fields: false
      row:
        type: graphql_entity
        options: {  }
      display_extenders: {  }
      graphql_query_name: pbInfoWithLastGallery
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user
        - user.roles
      tags: {  }
