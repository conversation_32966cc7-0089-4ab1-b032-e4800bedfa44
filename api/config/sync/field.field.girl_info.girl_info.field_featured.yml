uuid: 55df2ff2-e3c2-4bb4-b4db-0a584f4a0b4a
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_featured
  module:
    - pb_girl_info
id: girl_info.girl_info.field_featured
field_name: field_featured
entity_type: girl_info
bundle: girl_info
label: Featured
description: 'Display the Girl Info on the Homepage'
required: false
translatable: false
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: 'On'
  off_label: 'Off'
field_type: boolean
