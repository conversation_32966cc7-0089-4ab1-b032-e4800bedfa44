uuid: a9aeac4f-3b8e-488f-a0a3-e246a3c5b18d
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_slideshow
    - node.type.homepage
  module:
    - image
id: node.homepage.field_slideshow
field_name: field_slideshow
entity_type: node
bundle: homepage
label: Slideshow
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: false
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
