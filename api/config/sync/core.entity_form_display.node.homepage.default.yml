uuid: bffdc904-e531-4182-802e-50bee519b93a
langcode: en
status: true
dependencies:
  config:
    - field.field.node.homepage.field_banner_price
    - field.field.node.homepage.field_banner_title
    - field.field.node.homepage.field_cover
    - field.field.node.homepage.field_featured_video
    - field.field.node.homepage.field_features
    - field.field.node.homepage.field_grid_subtitle1
    - field.field.node.homepage.field_grid_subtitle2
    - field.field.node.homepage.field_grid_subtitle3
    - field.field.node.homepage.field_grid_title1
    - field.field.node.homepage.field_grid_title2
    - field.field.node.homepage.field_grid_title3
    - field.field.node.homepage.field_hero_slideshow
    - field.field.node.homepage.field_maintenance_mode
    - field.field.node.homepage.field_slideshow
    - field.field.node.homepage.field_subtitle
    - field.field.node.homepage.field_title
    - field.field.node.homepage.field_video_title
    - image.style.thumbnail
    - node.type.homepage
    - workflows.workflow.editorial
  module:
    - content_moderation
    - field_group
    - media_library
    - paragraphs
    - path
    - scheduler
    - scheduler_content_moderation_integration
    - svg_image
third_party_settings:
  field_group:
    group_hero:
      children:
        - field_slideshow
        - field_hero_slideshow
        - field_subtitle
        - field_title
      label: Hero
      region: content
      parent_name: ''
      weight: 4
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        description: ''
        required_fields: true
    group_grid:
      children:
        - group_first_column
        - group_second_column
        - group_third_column
      label: Grid
      region: content
      parent_name: ''
      weight: 16
      format_type: tabs
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        direction: vertical
        width_breakpoint: 640
    group_first_column:
      children:
        - field_grid_title1
        - field_grid_subtitle1
      label: 'First column'
      region: content
      parent_name: group_grid
      weight: 27
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_second_column:
      children:
        - field_grid_title2
        - field_grid_subtitle2
      label: 'Second Column'
      region: content
      parent_name: group_grid
      weight: 28
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_third_column:
      children:
        - field_grid_title3
        - field_grid_subtitle3
      label: 'Third Column'
      region: content
      parent_name: group_grid
      weight: 29
      format_type: tab
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        formatter: closed
        description: ''
        required_fields: true
    group_banner:
      children:
        - field_cover
        - field_banner_title
        - field_banner_price
      label: Banner
      region: content
      parent_name: ''
      weight: 17
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        description: ''
        required_fields: true
    group_video:
      children:
        - field_video_title
        - field_featured_video
      label: Video
      region: content
      parent_name: ''
      weight: 18
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        description: ''
        required_fields: true
id: node.homepage.default
targetEntityType: node
bundle: homepage
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_banner_price:
    type: string_textfield
    weight: 17
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_banner_title:
    type: string_textfield
    weight: 16
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_cover:
    type: media_library_widget
    weight: 15
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_featured_video:
    type: media_library_widget
    weight: 20
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_features:
    type: entity_reference_paragraphs
    weight: 34
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
    third_party_settings: {  }
  field_grid_subtitle1:
    type: string_textfield
    weight: 21
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_grid_subtitle2:
    type: string_textfield
    weight: 22
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_grid_subtitle3:
    type: string_textfield
    weight: 23
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_grid_title1:
    type: string_textfield
    weight: 20
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_grid_title2:
    type: string_textfield
    weight: 21
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_grid_title3:
    type: string_textfield
    weight: 22
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_hero_slideshow:
    type: media_library_widget
    weight: 31
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_maintenance_mode:
    type: boolean_checkbox
    weight: 35
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_slideshow:
    type: image_image
    weight: 30
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_subtitle:
    type: string_textfield
    weight: 32
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_title:
    type: string_textfield
    weight: 33
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_video_title:
    type: string_textfield
    weight: 19
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 13
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 12
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_state:
    type: scheduler_moderation
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  scheduler_settings:
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 14
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_state:
    type: scheduler_moderation
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
